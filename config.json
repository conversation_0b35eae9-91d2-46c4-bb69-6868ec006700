{"isAuthentication": false, "showLogo": true, "systemName": "训练数据", "clientId": "333", "clientSecret": "555", "loginPath": "https://192.168.120.155:4435", "newloginPath": "https://chat.deepseek.com", "dataCenterPath": "http://192.168.65.31:8082", "grapdatabsePath": "http://192.168.120.89:9099", "upload": {"type": ["sh", "py"], "size": 5242880}, "switchSystemList": [{"title": "活动空间", "icon": "activitySpaceSystem", "url": "https://***************:8080/activitySpaceSystem", "switchMode": "_self", "readme": "switchMode的说明：默认是_self替换当前页面,若需打开新标签页，请设置为_blank"}, {"title": "知识空间", "icon": "knowledgeSpaceSystem", "url": "https://***************:8080/knowledgeSpaceSystem", "switchMode": "_self"}, {"title": "数字空间", "icon": "digitSpaceSystem", "url": "https://***************:8080/digitSpaceSystem", "switchMode": "_self"}], "isLimitScriptFile": false, "menu": {"dataCollection": {"title": "数据采集", "visible": true, "meta": {"icon": "uxd-icon-fullscreen-exit"}, "children": {"strategy": {"title": "采集策略管理", "visible": true, "meta": {"icon": "uxd-icon-fullscreen-exit"}}, "oid": {"title": "采集OID管理", "visible": true, "meta": {"icon": "uxd-icon-fullscreen-exit"}}, "monitoring": {"title": "采集点位监控", "visible": true, "meta": {"icon": "uxd-icon-fullscreen-exit"}, "children": {"terminal": {"title": "终端节点", "visible": true, "meta": {"switchCountLimit": 20}}, "flow": {"title": "流量探针", "visible": true, "meta": {"refreshInterval": 5}}}}}}, "dataService": {"title": "数据服务", "visible": true, "meta": {"icon": "uxd-icon-menu-filled"}, "children": {"source": {"title": "数据源管理", "visible": true, "meta": {"icon": "uxd-icon-menu-filled", "baseUrl": "dataCenterPath", "iframeUrl": "/datax-database-sync/jdbc-datasource"}}, "directory": {"title": "数据目录", "visible": true, "meta": {"icon": "uxd-icon-menu-filled", "baseUrl": "dataCenterPath", "iframeUrl": "/datax-database-sync/data-directory"}}, "api": {"title": "API接口管理", "visible": true, "meta": {"icon": "uxd-icon-menu-filled", "baseUrl": "dataCenterPath", "iframeUrl": "/datax-data-service/visual-api-list"}}, "permission": {"title": "访问权限管理", "visible": true, "meta": {"icon": "uxd-icon-menu-filled", "baseUrl": "dataCenterPath", "iframeUrl": "/datax-data-service/role-manage"}}, "reportStatistics": {"title": "报表统计", "visible": true, "meta": {"icon": "uxd-icon-menu-filled"}, "children": {"statistics": {"title": "调用统计", "visible": true, "meta": {"baseUrl": "dataCenterPath", "iframeUrl": "/datax-data-service/api-time"}}, "dataReport": {"title": "数据报表", "visible": true, "meta": {}}}}, "viewSearch": {"title": "视图检索", "visible": true, "meta": {"icon": "uxd-icon-menu-filled"}, "children": {"dataSearch": {"title": "数据检索", "visible": true, "meta": {"limitMoudlesCount": 500}}, "dataView": {"title": "数据视图", "visible": false, "meta": {"baseUrl": "dataCenterPath", "iframeUrl": "/dashboard/index"}}, "dataViewNew": {"title": "数据视图", "visible": true}}}}}, "data": {"title": "大数据", "visible": true, "meta": {"icon": "uxd-icon-structure-filled"}, "children": {"visualAnalysis": {"title": "可视化分析", "visible": true, "meta": {"icon": "uxd-icon-structure-filled"}, "children": {"dashboard": {"title": "仪表盘管理", "visible": true, "meta": {}}, "pivotAnalysis": {"title": "透视分析", "visible": true, "meta": {}}}}, "operation": {"title": "运维监控", "visible": true, "meta": {"icon": "uxd-icon-structure-filled"}, "children": {"storeOperation": {"title": "存储引擎", "visible": true, "meta": {}}, "calculationOperation": {"title": "计算引擎", "visible": true, "meta": {}}}}, "resourceView": {"title": "监控视图", "visible": true, "meta": {"icon": "uxd-icon-structure-filled"}}, "resource": {"title": "资源监控", "visible": true, "meta": {"icon": "uxd-icon-structure-filled"}, "children": {"resourcehost": {"title": "主机", "visible": true, "meta": {}}, "resourcealarm": {"title": "警报", "visible": true, "meta": {}}}}, "dev": {"title": "数据开发", "visible": false, "meta": {"icon": "uxd-icon-structure-filled"}}, "taskManage": {"title": "任务调度", "visible": true, "meta": {"icon": "uxd-icon-structure-filled"}, "children": {"devEnvSetting": {"title": "开发环境配置", "visible": true, "meta": {"baseUrl": "dataCenterPath", "iframeUrl": "/datax-data-development/dev-env-setting"}}, "devEnvUpload": {"title": "作业列表查询", "visible": true, "meta": {"baseUrl": "dataCenterPath", "iframeUrl": "/datax-data-development/dev-env-upload"}}, "jarTaskDev": {"title": "JAR任务开发", "visible": true, "meta": {"baseUrl": "dataCenterPath", "iframeUrl": "/datax-data-development/jar-task-dev"}}, "developmentList": {"title": "开发任务列表", "visible": true, "meta": {"baseUrl": "dataCenterPath", "iframeUrl": "/datax-data-development/development-list"}}, "runTaskList": {"title": "作业运维列表", "visible": true, "meta": {"baseUrl": "dataCenterPath", "iframeUrl": "/datax-data-development/run-task-list"}}}}, "storageStatistics": {"title": "存储统计", "visible": true, "meta": {"icon": "uxd-icon-structure-filled"}}, "graphdatabaseAnalysis": {"title": "图数据库分析", "visible": true, "meta": {"icon": "uxd-icon-structure-filled", "baseUrl": "grapdatabsePath", "iframeUrl": "/"}}, "tenancyResource": {"title": "租户资源管理", "visible": true, "meta": {"icon": "uxd-icon-structure-filled"}}}}}, "table": {"strategy": [{"prop": "device", "label": "设备类型"}, {"prop": "describe", "label": "策略名称"}, {"prop": "execCommand", "label": "执行命令"}, {"prop": "execCycle", "label": "周期时间"}, {"prop": "execDateType", "label": "周期类型"}, {"prop": "strategyType", "label": "策略类型"}, {"prop": "source", "label": "策略来源"}, {"prop": "state", "label": "启用状态"}, {"prop": "createTime", "label": "添加时间", "width": 180}, {"prop": "updateTime", "label": "修改时间", "width": 180}], "oid": [{"prop": "deviceType", "label": "设备类型"}, {"prop": "oid", "label": "OID"}, {"prop": "name", "label": "采集名称"}, {"prop": "execCycle", "label": "周期时间"}, {"prop": "execDateType", "label": "周期类型"}, {"prop": "snmpTargetIp", "label": "实装装备IP"}, {"prop": "snmpTargetPort", "label": "实装装备端口"}, {"prop": "createTime", "label": "添加时间", "width": 180}, {"prop": "updateTime", "label": "修改时间", "width": 180}, {"prop": "state", "label": "启用状态"}], "terminal": [{"prop": "nodeId", "label": "点位名称"}, {"prop": "nodeType", "label": "设备类型"}, {"prop": "commType", "label": "通信类型"}, {"prop": "nodeIp", "label": "点位位置"}, {"prop": "sceneId", "label": "点位编号"}, {"prop": "type", "label": "点位类型"}, {"prop": "state", "label": "点位状态"}, {"prop": "gatherConfig", "label": "点位参数"}, {"prop": "collectFrequency", "label": "采集频率"}, {"prop": "heartbeatTime", "label": "心跳时间", "width": 180}, {"prop": "dataPushTime", "label": "数据推送时间"}], "dataDev": [{"prop": "taskName", "label": "任务名称", "rules": [{"required": true, "message": "请输入任务名称", "trigger": "blur"}]}, {"prop": "component", "label": "处理组件", "rules": [{"required": true, "message": "请选择处理组件", "trigger": "blur"}]}, {"prop": "storeTable", "label": "存储表", "rules": [{"required": true, "message": "请输入存储表", "trigger": "blur"}]}, {"prop": "dataName", "label": "数据名称", "rules": [{"required": true, "message": "请输入数据名称", "trigger": "blur"}]}, {"prop": "processingCycle", "label": "处理周期", "CanTable": false, "rules": [{"required": true, "message": "请输入Cron表达式", "trigger": "blur"}]}, {"prop": "cpuCores", "label": "CPU核数", "CanTable": false, "rules": [{"required": true, "message": "请输入任务需要的CPU核数", "trigger": "blur"}]}, {"prop": "memoryCapaity", "label": "内存(MB)", "CanTable": false, "rules": [{"required": true, "message": "请输入任务需要的内存大小", "trigger": "blur"}]}, {"prop": "handleLogic", "label": "处理逻辑", "CanTable": false, "rules": [{"required": true, "message": "请根据组件输入处理逻辑", "trigger": "blur"}]}, {"prop": "runStatus", "label": "运行状态", "canAdd": false, "canEdit": false}, {"prop": "durationTime", "label": "持续时间", "canAdd": false, "canEdit": false}, {"prop": "createTime", "label": "创建时间", "canAdd": false, "canEdit": false}], "operation": [{"prop": "componentName", "label": "服务名称", "rules": [{"required": true, "message": "请输入组件名称", "trigger": "blur"}]}], "hostComponent": [{"prop": "<PERSON><PERSON><PERSON>", "label": "角色", "width": 150}, {"prop": "runningStatus", "label": "运行状态", "width": 130}, {"prop": "config<PERSON><PERSON>us", "label": "配置状态", "width": 130, "show": false}, {"prop": "hostname", "label": "主机名称", "width": 150}, {"prop": "ip", "label": "管理IP", "width": 150, "show": false}, {"prop": "businessIp", "label": "业务IP", "width": 150}, {"prop": "rack", "label": "机架", "width": 150}, {"prop": "instanceGroupName", "label": "所属实例组"}], "dataAlarm": [{"prop": "name", "label": "告警名称"}, {"prop": "id", "label": "告警id"}, {"prop": "level", "label": "告警级别"}, {"prop": "occurTime", "label": "产生时间"}, {"prop": "object", "label": "告警对象"}, {"prop": "location", "label": "定位信息"}], "dataResource": [{"prop": "hostname", "label": "主机名称", "width": 160}, {"prop": "ip", "label": "管理IP", "width": 140}, {"prop": "runningStatus", "label": "运行状态", "width": 100}, {"prop": "cpuCores", "label": "CPU核数", "width": 100}, {"prop": "cpuUsage", "label": "CPU使用率", "width": 200, "show": false}, {"prop": "memoryUsage", "label": "内存（GB）"}, {"prop": "diskUsage", "label": "磁盘（GB）"}, {"prop": "networkRead", "label": "网络接收速度（Byte/s）", "width": 200, "show": false}, {"prop": "networkWrite", "label": "网络发送速度（Byte/s）", "width": 200, "show": false}], "dashboard": [{"prop": "name", "label": "名称", "rules": [{"required": true, "message": "请输入仪表盘名称", "trigger": "blur"}, {"min": 1, "max": 50, "message": "长度在1-50个字符", "trigger": ["blur", "change"]}]}, {"prop": "comment", "label": "备注", "rules": [{"max": 200, "message": "长度最多为200个字符", "trigger": ["blur", "change"]}]}], "pivotAnalysis": [{"prop": "name", "label": "名称"}, {"prop": "comment", "label": "备注"}], "storageStatistics": [{"prop": "name", "label": "索引名称"}, {"prop": "type", "label": "类型", "width": 150}, {"prop": "doc<PERSON>ount", "label": "文档数量", "width": 150}, {"prop": "diskUse", "label": "磁盘占用", "width": 150}], "dataView": [{"prop": "sceneId", "label": "场景ID"}, {"prop": "scene<PERSON><PERSON>", "label": "场景名称"}, {"prop": "taskType", "label": "任务类型"}, {"prop": "level", "label": "难度"}, {"prop": "createTime", "label": "创建时间"}]}}