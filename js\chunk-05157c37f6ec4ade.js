(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-05157c37"],{"02bd":function(t,e,n){"use strict";var s=n("ad1b"),a=n.n(s);a.a},"03a7":function(t,e,n){"use strict";var s=n("f6f4"),a=n.n(s);a.a},"0cb5":function(t,e,n){t.exports=n.p+"static/img/dataHandleSystem.72388a11.png"},3722:function(t,e,n){t.exports=n.p+"static/img/active-menu.07f13e03.svg"},"3b51":function(t,e,n){},"3e3e":function(t,e,n){t.exports=n.p+"static/img/host.9233f1d2.png"},"43cd":function(t,e,n){"use strict";var s=n("3b51"),a=n.n(s);a.a},4450:function(t,e,n){t.exports=n.p+"static/img/activitySpaceSystem.e903b2a4.png"},"822e":function(t,e,n){"use strict";var s=n("c0d3"),a=n.n(s);a.a},"9a36":function(t,e,n){},"9fbd":function(t,e,n){"use strict";var s=n("9a36"),a=n.n(s);a.a},ad1b:function(t,e,n){},b807:function(t,e,n){t.exports=n.p+"static/img/digitSpaceSystem.ab353f7f.png"},c0d3:function(t,e,n){},c1f7:function(t,e,n){"use strict";n.r(e);var s=function(){var t=this,e=t.$createElement,n=t._self._c||e;return n("uxd-container",{class:t.prefixCls},[n("uxd-header",{class:t.prefixCls+"--header",attrs:{height:"80px"}},[n("logo"),t._v(" "),n("nav-bar"),t._v(" "),n("right-menu")],1),t._v(" "),n("uxd-container",{class:t.prefixCls+"--container"},[n("uxd-aside",{class:t.prefixCls+"--aside",attrs:{width:"236px"}},[n("side-bar")],1),t._v(" "),n("uxd-main",{class:t.prefixCls+"--main"},[n("section",{staticClass:"breadcrumb"},[t._v("\n                当前位置：\n                "),t._v(" "),t._l(t.$route.matched.slice(1),(function(e,s){return n("span",{key:e.path,staticClass:"breadcrumb-item"},[t._v("\n                    "+t._s(e.meta.title)+"\n                    "),s!==t.$route.matched.slice(1).length-1?n("span",[t._v("\n                        >\n                    ")]):t._e()])}))],2),t._v(" "),n("section",{staticClass:"main"},[n("router-view")],1)])],1)],1)},a=[],c=n("ef44"),i=function(){var t=this,e=t.$createElement,n=t._self._c||e;return n("section",{class:t.prefixCls,on:{click:t.linkTo}},[t.system.config.showLogo?n("uxd-image",{attrs:{src:"./logo.png"}}):n("p",{staticClass:"system-name"},[t._v(t._s(t.system.config.systemName))])],1)},o=[],r=n("5530"),u=(n("14d9"),n("2f62")),l={data:function(){return{}},computed:Object(r["a"])({prefixCls:function(){var t=Object(c["e"])("logo"),e=t.prefixCls;return e}},Object(u["b"])(["system"])),methods:{linkTo:function(){this.$router.push("/")}}},d=l,m=(n("822e"),n("2877")),p=Object(m["a"])(d,i,o,!1,null,"579a3ea1",null),f=p.exports,h=function(){var t=this,e=t.$createElement,n=t._self._c||e;return n("section",{class:t.prefixCls},[n("uxd-popover",{attrs:{"popper-class":"switch-system",placement:"bottom-end",title:"管理空间切换",trigger:"click",width:"560",transition:""}},[n("div",{staticClass:"switch-system--list"},t._l(t.getSystemList(),(function(e){return n("div",{key:e.title,staticClass:"switch-system--item",on:{click:function(n){return t.handleSwitchSystem(e)}}},[n("p",{staticClass:"switch-system--item--title"},[n("uxd-image",{staticClass:"icon",attrs:{src:t.getSystemImgUrl(e.icon)}}),t._v(" "),n("span",{staticClass:"title"},[t._v("\n                        "+t._s(e.title)+"\n                    ")])],1),t._v(" "),n("svg-icon",{attrs:{name:"switch2"}})],1)})),0),t._v(" "),n("div",{staticClass:"text",attrs:{slot:"reference"},slot:"reference"},[n("div",{staticClass:"switch-system top-menu-btn"},[n("svg-icon",{attrs:{name:"switch-system"}}),t._v(" "),n("span",{staticClass:"text"},[t._v("切换")])],1)])]),t._v(" "),n("div",{staticClass:"inotify top-menu-btn"},[n("svg-icon",{attrs:{name:"inotify"}})],1),t._v(" "),n("div",{staticClass:"fullscreen top-menu-btn",on:{click:t.handleFullsrceen}},[n("svg-icon",{attrs:{name:"fullscreen"}})],1),t._v(" "),n("uxd-divider",{attrs:{direction:"vertical"}}),t._v(" "),n("div",{staticClass:"account"},[n("div",{staticClass:"account-icon"},[n("svg-icon",{staticClass:"account-bg",attrs:{name:"account-bg"}}),t._v(" "),n("svg-icon",{staticClass:"account-person",attrs:{name:"account-person"}})],1),t._v(" "),n("p",{staticClass:"account-name",on:{click:t.handleOpenAccountMenu}},[n("span",{staticClass:"name"},[t._v("\n                "+t._s(t.userInfo&&t.userInfo.user_name||"超级管理员")+"\n            ")]),t._v(" "),n("svg-icon",{staticClass:"arrow-down",attrs:{name:"arrow-down"}})],1),t._v(" "),n("uxd-cascader",{ref:"accountNameCascaderRef",staticClass:"account-name-cascader",attrs:{props:{expandTrigger:"hover"},options:t.options,"popper-class":"account-name-cascader-popper"},on:{change:t.handleMenu},model:{value:t.menu,callback:function(e){t.menu=e},expression:"menu"}})],1)],1)},v=[],g=(n("4ec9"),n("d3b7"),n("3ca3"),n("ddb0"),n("7db0"),n("ac1f"),n("5319"),n("159b"),n("9861"),n("99af"),n("25f0"),n("b6df")),_=n("4360"),b=n("8c55"),y=n("ac6e"),x={components:{},data:function(){return{menu:[],options:[{value:"3",label:"退出登录",handle:this.handleLoginOut}],systemIconMap:new Map([["1","digitSpaceSystem"],["2","knowledgeSpaceSystem"],[null,"activitySpaceSystem"],["4","dataHandleSystem"]])}},computed:Object(r["a"])(Object(r["a"])({prefixCls:function(){var t=Object(c["e"])("right-menu"),e=t.prefixCls;return e}},Object(u["b"])(["userInfo"])),Object(u["b"])(["system"])),methods:{getSystemImgUrl:function(t){return n("e078")("./".concat(t,".png"))},handleSwitchSystem:function(t){console.log(t.redirect_url,"切换系统"),window.open(t.redirect_url,t.switchMode)},handleFullsrceen:function(){Object(c["f"])()},handleOpenAccountMenu:function(){this.menu=[],this.$refs.accountNameCascaderRef.toggleDropDownVisible(!0)},handleMenu:function(t){var e=this.options.find((function(e){return e.value==t[0]}));e&&e.handle()},handleLoginOut:function(){var t=this,e=_["a"].state.system;e&&e.config&&Object(g["a"])({client_id:e.config.clientId,client_secret:e.config.clientSecret}).then((function(t){Object(b["b"])(y["a"]);var n=e.config.newloginPath;n?window.location.replace(n):console.log("未获取到退出失败的登录地址")})).catch((function(e){console.log(e,"logoutApi"),t.$message.error("退出失败！")}))},getSystemList:function(){var t=this,e=_["a"].state.user,n=e.userInfo,s=e.authInfo,a=_["a"].state.system.config,c=[];if(n&&n.role_menu&&n.role_menu.length>0&&n.role_menu[0].systems)n.role_menu[0].systems.forEach((function(e){if("训练数据"!=e.system_name&&"4"!=e.system_icon){var n={title:e.system_name,icon:t.systemIconMap.get(e.system_icon)||"activitySpaceSystem",redirect_url:e.redirect_url,switchMode:"_self"};c.push(n)}}));else{var i,o=new URLSearchParams;o.append("account",encodeURIComponent((null===n||void 0===n?void 0:n.user_account)||"")),o.append("login_token",encodeURIComponent((null===s||void 0===s?void 0:s.loginToken)||"")),null===a||void 0===a||null===(i=a.switchSystemList)||void 0===i||i.forEach((function(t){t.redirect_url="".concat(t.url,"?").concat(o.toString()),c.push(t)}))}return c}}},C=x,w=(n("9fbd"),Object(m["a"])(C,h,v,!1,null,"7dd5831d",null)),S=w.exports,O=function(){var t=this,e=t.$createElement,n=t._self._c||e;return n("uxd-menu",{attrs:{"default-active":t.activeIndex,mode:"horizontal"},on:{select:t.selectMenu}},t._l(t.routeList,(function(e,s){return n("uxd-menu-item",{key:s,style:{background:t.activeIndex===e.path?"url('"+t.ActiveMenu+"') no-repeat":null},attrs:{index:e.path}},[n("p",{staticClass:"label"},[n("uxd-icon-pro",{attrs:{name:e.meta.icon}}),t._v("\n            "+t._s(e.meta.title)+"\n        ")],1)])})),1)},k=[],j=(n("d81d"),n("3722")),$=n.n(j),M={data:function(){return{routeList:[],ActiveMenu:$.a}},computed:Object(r["a"])(Object(r["a"])({},Object(u["b"])(["system"])),{},{activeIndex:function(){return this.$route.matched[1].path}}),watch:{"system.routes":{handler:function(t){var e=[];t.map((function(t){t.meta.hidden||e.push(t)})),this.routeList=e},deep:!0,immediate:!0}},methods:{selectMenu:function(t){this.$router.push(t)}}},I=M,L=(n("02bd"),Object(m["a"])(I,O,k,!1,null,"c8655210",null)),E=L.exports,N=function(){var t=this,e=t.$createElement,n=t._self._c||e;return n("uxd-menu",{attrs:{"default-openeds":t.defaultOpeneds},on:{select:t.selectMenu}},[t._l(t.routeList,(function(e,s){return[e.children&&0!==e.children.length&&!1!==e.meta.isShowChildren?n("uxd-submenu",{key:s,attrs:{index:e.path}},[n("template",{slot:"title"},[e.meta.icon?n("uxd-icon-pro",{attrs:{name:e.meta.icon}}):t._e(),t._v("\n                "+t._s(e.meta.title)+"\n            ")],1),t._v(" "),t._l(e.children,(function(e,s){return n("uxd-menu-item",{key:s,class:t.$route.path===e.path?"is-active":"",style:{background:t.$route.path===e.path?t.getBgColor():null,color:t.$route.path===e.path?t.getBgColor():null},attrs:{index:e.path}},[e.meta.icon?n("uxd-icon-pro",{attrs:{name:e.meta.icon}}):t._e(),t._v("\n                "+t._s(e.meta.title)+"\n            ")],1)}))],2):n("uxd-menu-item",{key:s,staticClass:"menu-one-level",class:t.$route.path===e.path?"is-active":"",style:{background:t.$route.path===e.path?t.getBgColor():null},attrs:{index:e.path}},[e.meta.icon?n("uxd-icon-pro",{attrs:{name:e.meta.icon}}):t._e(),t._v("\n            "+t._s(e.meta.title)+"\n        ")],1)]}))],2)},U=[],B=n("3835"),R={data:function(){return{routeList:[],defaultOpeneds:[]}},computed:Object(r["a"])({},Object(u["b"])(["system"])),watch:{$route:{handler:function(){var t=this,e=Object(B["a"])(this.$route.matched,2),n=e[1],s=[];this.system.routes.map((function(e){var a=e.path,c=e.children;a===n.path&&(t.routeList=c||[],c.map((function(t){s.push(t.path)})))})),this.defaultOpeneds=s},deep:!0,immediate:!0}},methods:{selectMenu:function(t){this.$router.push(t)},getBgColor:function(){return"linear-gradient(\n                270deg,\n                rgba(28, 85, 195, 0.5) 0%,\n                rgba(28, 85, 195, 1) 100%\n            )"}}},A=R,T=(n("03a7"),Object(m["a"])(A,N,U,!1,null,"0545429b",null)),D=T.exports,F={components:{Logo:f,RightMenu:S,NavBar:E,SideBar:D},data:function(){return{}},computed:{prefixCls:function(){var t=Object(c["e"])("layout"),e=t.prefixCls;return e},routeName:function(){return this.$route.matched[1].meta.title}}},H=F,J=(n("43cd"),Object(m["a"])(H,s,a,!1,null,"b86700fe",null));e["default"]=J.exports},cd48:function(t,e,n){t.exports=n.p+"static/img/knowledgeSpaceSystem.fd3b8fd2.png"},e078:function(t,e,n){var s={"./activitySpaceSystem.png":"4450","./bg.png":"3f6e","./dataHandleSystem.png":"0cb5","./digitSpaceSystem.png":"b807","./host.png":"3e3e","./knowledgeSpaceSystem.png":"cd48"};function a(t){var e=c(t);return n(e)}function c(t){if(!n.o(s,t)){var e=new Error("Cannot find module '"+t+"'");throw e.code="MODULE_NOT_FOUND",e}return s[t]}a.keys=function(){return Object.keys(s)},a.resolve=c,t.exports=a,a.id="e078"},f6f4:function(t,e,n){}}]);