(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-19dd4163"],{"3dd8":function(e,t,i){"use strict";i.d(t,"b",(function(){return a})),i.d(t,"a",(function(){return r})),i.d(t,"c",(function(){return s}));i("4ec9"),i("d3b7"),i("3ca3"),i("ddb0");var a=new Map([[0,"流量采集"],[1,"终端采集"],[2,"协议采集"]]),r=new Map([[0,"系统自带"],[1,"手动添加"]]),s=new Map([["M","分钟"],["H","小时"]])},"7b54":function(e,t,i){"use strict";i.d(t,"a",(function(){return a}));i("4ec9"),i("d3b7"),i("3ca3"),i("ddb0");var a=new Map([[0,"未启用"],[1,"已启用"],[-1,"异常"]])},a045:function(e,t,i){},bc8b:function(e,t,i){"use strict";i.r(t);var a=function(){var e=this,t=e.$createElement,i=e._self._c||t;return i("edr-table",{directives:[{name:"loading",rawName:"v-loading",value:e.loading,expression:"loading"}],ref:"tableRef",attrs:{tableFieldList:e.tableFieldList,api:e.oidListApi,params:e.queryList},scopedSlots:e._u([{key:"execDateType",fn:function(t){var a=t.row;return[i("span",[e._v("\n            "+e._s(e.execDateType_Map.get(a.execDateType))+"\n        ")])]}},{key:"state",fn:function(t){var a=t.row;return[i("span",{class:1!=a.state?"state-danger":null},[e._v("\n            "+e._s(e.STATUS_MAP.get(a.state))+"\n        ")])]}}])},[i("template",{slot:"search-header-form"},[i("uxd-form-item",{attrs:{label:"采集名称"}},[i("uxd-input",{attrs:{placeholder:"请输入采集名称"},model:{value:e.queryList.name,callback:function(t){e.$set(e.queryList,"name",t)},expression:"queryList.name"}})],1),e._v(" "),i("uxd-form-item",{attrs:{label:"设备类型"}},[i("cascader",{attrs:{list:e.queryList.deviceType,popperClass:"table-search-header"},on:{"update:list":function(t){return e.$set(e.queryList,"deviceType",t)}}})],1),e._v(" "),i("uxd-form-item",{attrs:{label:"状态"}},[i("uxd-select",{attrs:{placeholder:"请选择状态",clearable:""},model:{value:e.queryList.state,callback:function(t){e.$set(e.queryList,"state",t)},expression:"queryList.state"}},e._l(e.STATUS_MAP,(function(e){var t=e[0],a=e[1];return i("uxd-option",{key:t,attrs:{value:t,label:a}})})),1)],1)],1),e._v(" "),i("template",{slot:"search-header-opt"},[i("span",{staticClass:"confirm-btn search-btn",on:{click:e.search}},[e._v("\n            搜索\n        ")]),e._v(" "),i("span",{staticClass:"cancel-btn reset-btn",on:{click:e.reset}},[e._v("\n            重置\n        ")])]),e._v(" "),i("template",{slot:"opt-header"},[i("span",{staticClass:"confirm-btn",on:{click:function(t){return e.openFormDialog()}}},[e._v("\n            新增\n        ")])]),e._v(" "),e._v(" "),e._v(" "),i("uxd-table-column",{attrs:{width:"150px",label:"操作"},scopedSlots:e._u([{key:"default",fn:function(t){var a=t.row;return[i("span",{staticClass:"operation",on:{click:function(t){return e.openFormDialog(a)}}},[e._v("编辑")]),e._v(" "),i("span",{staticClass:"operation",on:{click:function(t){return e.handleDelete(a)}}},[e._v("删除")]),e._v(" "),i("uxd-popconfirm",{attrs:{icon:"uxd-icon-info","icon-color":"red",title:"您确定要"+(1===a.state?"停用":"启用")+"吗？"},on:{confirm:function(t){return e.handleState(a)}}},[i("span",{staticClass:"operation",attrs:{slot:"reference"},slot:"reference"},[e._v("\n                    "+e._s(1===a.state?"停用":"启用")+"\n                ")])])]}}])}),e._v(" "),i("form-dialog",{attrs:{visible:e.dialog.visible},on:{"update:visible":function(t){return e.$set(e.dialog,"visible",t)},refresh:e.search},model:{value:e.dialog.data,callback:function(t){e.$set(e.dialog,"data",t)},expression:"dialog.data"}})],2)},r=[],s=i("c7eb"),n=i("1da1"),o=(i("ac1f"),i("841c"),i("d3b7"),i("b0c0"),i("99af"),i("25f0"),i("3ca3"),i("ddb0"),i("9861"),i("d232")),c=i("4f8d");function l(e){return o["a"].get({url:c["d"].oid,params:e})}function d(e){return o["a"].post({url:c["d"].switchOid,params:e})}function u(e){return o["a"].post({url:c["d"].addOid,params:e})}function p(e){return o["a"].post({url:c["d"].updateOid,params:e})}function f(e){return o["a"].post({url:c["d"].deleteOid,params:e})}function m(e){return o["a"].get({url:c["d"].getOidDict,params:e})}function v(e){return o["a"].post({url:c["d"].addOidDict,params:e})}function b(e){return o["a"].get({url:"".concat(c["d"].deleteOidDict,"?").concat(new URLSearchParams(e).toString()),params:{}})}var g=i("7b54"),h=function(){var e=this,t=e.$createElement,i=e._self._c||t;return i("uxd-dialog",{attrs:{title:e.queryList.id?"编辑采集OID":"新增采集OID",visible:e.visible,width:"557px","before-close":e.close,"append-to-body":"","custom-class":"oid-dialog"},on:{"update:visible":function(t){e.visible=t}}},[i("uxd-form",{ref:"formRef",attrs:{"label-width":"auto",model:e.queryList,rules:e.formRules,"label-suffix":"："}},[i("uxd-form-item",{attrs:{label:"设备类型",prop:"deviceId"}},[i("cascader",{attrs:{list:e.queryList.deviceId,popperClass:"dialog-form"},on:{"update:list":function(t){return e.$set(e.queryList,"deviceId",t)}}})],1),e._v(" "),i("uxd-form-item",{attrs:{label:"采集名称",prop:"name"}},[i("uxd-input",{attrs:{placeholder:"请输入采集名称"},model:{value:e.queryList.name,callback:function(t){e.$set(e.queryList,"name",t)},expression:"queryList.name"}})],1),e._v(" "),i("uxd-form-item",{attrs:{label:"OID",prop:"oid"}},[i("div",{staticStyle:{display:"flex"}},[i("uxd-select",{staticClass:"oid-dic-select",attrs:{filterable:"",multiple:"",placeholder:"请输入关键词","popper-class":"oid-dic-popper"},model:{value:e.queryList.oid,callback:function(t){e.$set(e.queryList,"oid",t)},expression:"queryList.oid"}},e._l(e.oidList,(function(t){return i("uxd-option",{key:t.oid,attrs:{label:t.name,value:t.oid}},[i("div",{staticClass:"oid-dic-select-item"},[i("uxd-popconfirm",{attrs:{title:"确定删除该数据？"},on:{confirm:function(i){return e.handleDeleteOidDic(t)}}},[i("uxd-button",{attrs:{slot:"reference","is-icon":"",icon:"uxd-icon-delete",disabled:0==t.source},on:{click:function(e){e.stopPropagation()}},slot:"reference"})],1),e._v(" "),i("p",[e._v(e._s(t.name))])],1)])})),1),e._v(" "),i("span",{staticClass:"confirm-btn",staticStyle:{"margin-left":"16px","margin-right":"0px"},on:{click:e.handleAddOidDic}},[e._v("\n                    新增\n                ")])],1)]),e._v(" "),i("uxd-form-item",{attrs:{label:"执行周期",prop:"execCycle"}},[i("div",{staticStyle:{display:"flex"}},[i("uxd-input-number",{attrs:{min:1,max:"H"==e.queryList.execDateType?59:23,precision:0,placeholder:"请输入执行周期（Quartz）"},model:{value:e.queryList.execCycle,callback:function(t){e.$set(e.queryList,"execCycle",t)},expression:"queryList.execCycle"}}),e._v(" "),i("uxd-radio-group",{staticStyle:{"flex-shrink":"0"},attrs:{slow:!1},model:{value:e.queryList.execDateType,callback:function(t){e.$set(e.queryList,"execDateType",t)},expression:"queryList.execDateType"}},[i("uxd-radio-button",{attrs:{label:"H"}},[e._v("小时")]),e._v(" "),i("uxd-radio-button",{attrs:{label:"M"}},[e._v("分钟")])],1)],1)]),e._v(" "),i("uxd-form-item",{attrs:{label:"实装装备IP",prop:"snmpTargetIp"}},[i("uxd-input",{attrs:{placeholder:"请输入实装装备IP"},model:{value:e.queryList.snmpTargetIp,callback:function(t){e.$set(e.queryList,"snmpTargetIp",t)},expression:"queryList.snmpTargetIp"}})],1),e._v(" "),i("uxd-form-item",{attrs:{label:"实装装备端口",prop:"snmpTargetPort"}},[i("uxd-input",{attrs:{placeholder:"请输入实装装备端口"},model:{value:e.queryList.snmpTargetPort,callback:function(t){e.$set(e.queryList,"snmpTargetPort",t)},expression:"queryList.snmpTargetPort"}})],1),e._v(" "),i("uxd-form-item",{attrs:{label:"是否启用"}},[i("uxd-switch",{attrs:{"active-value":1,"inactive-value":0},model:{value:e.queryList.state,callback:function(t){e.$set(e.queryList,"state",t)},expression:"queryList.state"}})],1)],1),e._v(" "),i("div",{staticClass:"dialog-footer",attrs:{slot:"footer"},slot:"footer"},[i("span",{staticClass:"cancel-btn",on:{click:e.close}},[e._v("取 消")]),e._v(" "),i("uxd-button",{staticClass:"confirm-btn",attrs:{loading:e.loading},on:{click:e.confirm}},[e._v("\n            确认\n        ")])],1),e._v(" "),i("common-add-edit-dialog",{attrs:{visible:e.dialog.addOidDicDialog.visible,title:e.dialog.addOidDicDialog.title,data:e.dialog.addOidDicDialog.data,config:e.dialog.addOidDicDialog.config},on:{"update:visible":function(t){return e.$set(e.dialog.addOidDicDialog,"visible",t)}}})],1)},y=[],x=(i("b64b"),i("a15b"),i("ed08")),L=(i("d9e2"),i("f17a")),q=function(e,t,i){Object(L["e"])(t)?i():i(new Error("请输入正确的IP"))},_=function(e,t,i){Object(L["h"])(t)?i():i(new Error("请输入正确的端口"))},D=i("f656"),O={props:{visible:{type:Boolean,default:!1},value:{type:Object,default:function(){return{}}}},data:function(){return{loading:!1,queryList:{deviceId:"",name:"",oid:"",state:1,id:"",execCycle:"",execDateType:"H",snmpTargetIp:"",snmpTargetPort:""},formRules:{deviceId:[{required:!0,message:"请选择设备类型",trigger:"change"}],name:[{required:!0,message:"请输入采集名称",trigger:"blur"},Object(D["b"])()],oid:[{required:!0,message:"请输入OID",trigger:"blur"}],execCycle:[{required:!0,message:"请输入执行周期",trigger:"blur"}],snmpTargetIp:[{required:!0,message:"请输入实装装备IP",trigger:"blur"},{validator:q,trigger:"blur"}],snmpTargetPort:[{required:!0,message:"请输入实装装备端口",trigger:"blur"},{validator:_,trigger:"blur"}]},oidList:[],dialog:{addOidDicDialog:{visible:!1,title:"新增",data:{},config:{props:[{prop:"name",label:"OID名称",rules:[{required:!0,message:"请输入OID名称",trigger:"blur"}]},{prop:"oid",label:"OID值",rules:[{required:!0,message:"请输入OID值",trigger:"blur"}]}],addApi:v}}}}},watch:{visible:{handler:function(e){if(e&&(this.getOidList(),0!==Object.keys(this.value).length)){var t=this.value,i=t.id,a=t.name,r=t.oid,s=t.deviceId,n=t.state,o=t.execCycle,c=t.execDateType,l=t.snmpTargetIp,d=t.snmpTargetPort;this.queryList.id=i,this.queryList.name=a,this.queryList.oid=r.split(","),this.queryList.deviceId=s,this.queryList.state=n,this.queryList.execCycle=o,this.queryList.execDateType=c,this.queryList.snmpTargetIp=l,this.queryList.snmpTargetPort=d}}}},methods:{close:function(){this.queryList={deviceId:"",name:"",oid:"",state:1,id:"",execCycle:"",execDateType:"H",snmpTargetIp:"",snmpTargetPort:""},this.$refs.formRef.resetFields(),this.$emit("update:visible",!1)},confirm:function(){var e=this;this.$refs["formRef"].validate(function(){var t=Object(n["a"])(Object(s["a"])().mark((function t(i){var a;return Object(s["a"])().wrap((function(t){while(1)switch(t.prev=t.next){case 0:if(!i){t.next=22;break}if(e.loading=!0,a=Object(x["a"])({},e.queryList),a.oid=a.oid.join(","),t.prev=4,!e.queryList.id){t.next=10;break}return t.next=8,p(a);case 8:t.next=13;break;case 10:return delete a.id,t.next=13,u(a);case 13:e.$message.success("操作成功"),e.$emit("refresh"),e.close(),t.next=21;break;case 18:t.prev=18,t.t0=t["catch"](4),console.log(t.t0);case 21:e.loading=!1;case 22:case"end":return t.stop()}}),t,null,[[4,18]])})));return function(e){return t.apply(this,arguments)}}())},getOidList:function(){var e=this;return Object(n["a"])(Object(s["a"])().mark((function t(){var i;return Object(s["a"])().wrap((function(t){while(1)switch(t.prev=t.next){case 0:return t.next=2,m();case 2:i=t.sent,200==i.code&&(e.oidList=i.data);case 4:case"end":return t.stop()}}),t)})))()},handleAddOidDic:function(){this.dialog.addOidDicDialog.data={},this.dialog.addOidDicDialog.visible=!0},handleDeleteOidDic:function(e){var t=this;return Object(n["a"])(Object(s["a"])().mark((function i(){var a;return Object(s["a"])().wrap((function(i){while(1)switch(i.prev=i.next){case 0:return i.next=2,b({id:e.id});case 2:a=i.sent,200==a.code&&t.getOidList();case 4:case"end":return i.stop()}}),i)})))()}}},T=O,w=(i("c2ba"),i("2877")),k=Object(w["a"])(T,h,y,!1,null,null,null),I=k.exports,$=i("3dd8"),C={components:{FormDialog:I},data:function(){return{oidListApi:l,STATUS_MAP:g["a"],execDateType_Map:$["c"],queryList:{name:"",deviceType:"",state:""},dialog:{visible:!1,data:{}},loading:!1}},computed:{tableFieldList:function(){var e,t,i;return(null===(e=this.$store.state.system)||void 0===e?void 0:null===(t=e.config)||void 0===t?void 0:null===(i=t.table)||void 0===i?void 0:i.oid)||[]}},methods:{openFormDialog:function(e){0!==(null===e||void 0===e?void 0:e.source)&&(this.dialog.data=e||{},this.dialog.visible=!0)},handleDelete:function(e){var t=this;this.$confirm("您确定删除该采集OID吗？","温馨提示",{type:"warning"}).then(Object(n["a"])(Object(s["a"])().mark((function i(){return Object(s["a"])().wrap((function(i){while(1)switch(i.prev=i.next){case 0:return i.next=2,f([e.id]);case 2:t.$message.success("删除成功"),t.search();case 4:case"end":return i.stop()}}),i)}))))},handleState:function(e){var t=this,i=e.id,a=e.state;this.loading=!0,d({id:i,state:0==a?1:0}).then((function(){t.$message.success("操作成功"),t.search()})).finally((function(){t.loading=!1}))},reset:function(){this.queryList.name="",this.queryList.deviceType="",this.queryList.state="",this.search()},search:function(){this.$refs.tableRef.getDataList(this.queryList)}}},j=C,P=Object(w["a"])(j,a,r,!1,null,null,null);t["default"]=P.exports},c2ba:function(e,t,i){"use strict";var a=i("a045"),r=i.n(a);r.a},f656:function(e,t,i){"use strict";i.d(t,"b",(function(){return a})),i.d(t,"a",(function(){return r})),i.d(t,"c",(function(){return s}));i("ac1f"),i("00b4"),i("d9e2");function a(){return{min:1,max:50,message:"长度在1-50个字符",trigger:["blur","change"]}}function r(){return{max:200,message:"长度最多为200个字符",trigger:["blur","change"]}}function s(){return{validator:function(e,t,i){var a=/^[a-zA-Z0-9!@#$%^&*()_=|\\（）+\/\u4e00-\u9fa5-]+$/;a.test(t)?i():i(new Error("只能包含中文、字母、数字和!@#$%^&*()_=|\\（）-+/"))},trigger:["blur","change"]}}}}]);