(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-1d09dd69"],{"087c":function(e,t,n){"use strict";var a=n("7804"),i=n.n(a);i.a},7804:function(e,t,n){},aae6:function(e,t,n){"use strict";n.r(t);var a=function(){var e=this,t=e.$createElement,n=e._self._c||t;return n("div",{class:e.classPrefix},[n("div",{class:e.classPrefix+"__search"},[e.isShowDetailSearch?e._e():n("div",{staticClass:"search-icon"},[n("p",{staticClass:"icon-1"},[e._v("关联")]),e._v(" "),n("p",{staticClass:"icon-2"},[e._v("检索")])]),e._v(" "),n("div",{staticClass:"search-input"},[n("uxd-select",{staticClass:"data-source",attrs:{clearable:"",loading:e.loadingDataSource,placeholder:"请选择","lazy-load":e.getSearchDataSource,data:e.dataSourceList,"props-options":{label:"datasourceName",value:"id"}},scopedSlots:e._u([{key:"opiton",fn:function(t){var n=t.item;return[e._v("\n                    "+e._s(n.label)+"\n                ")]}}]),model:{value:e.dataSource,callback:function(t){e.dataSource=t},expression:"dataSource"}}),e._v(" "),n("uxd-input",{staticClass:"uxd-input-group--search_append",attrs:{placeholder:"共"+e.moudleCount+"个模块"},model:{value:e.queryParam.keywords,callback:function(t){e.$set(e.queryParam,"keywords",t)},expression:"queryParam.keywords"}},[n("uxd-button",{attrs:{slot:"suffix","is-icon":"",icon:"uxd-icon-setting"},on:{click:e.handleShowSearchConfig},slot:"suffix"}),e._v(" "),n("uxd-button",{attrs:{slot:"append",type:"primary",icon:"uxd-icon-search"},on:{click:function(t){return e.handleSearchResult(!0)}},slot:"append"})],1)],1),e._v(" "),n("div",{staticClass:"search-contions",style:{visibility:e.isShowSearchConfig?"visible":"hidden"}},[n("ul",[e._e(),e._v(" "),n("li",[n("uxd-popover",{ref:"menuPopover",attrs:{"popper-class":"search-moudle-menu-popover",placement:"bottom",width:"600"},model:{value:e.menuConfig.menuConfig,callback:function(t){e.$set(e.menuConfig,"menuConfig",t)},expression:"menuConfig.menuConfig"}},[n("div",{staticClass:"element-modules__body"},e._l(e.menuConfig.modules,(function(t){return n("div",{key:t.name,staticClass:"element-modules__list"},[n("uxd-checkbox",{on:{change:function(n){return e.checkedAllMenu(n,t)}},model:{value:t.checkAll,callback:function(n){e.$set(t,"checkAll",n)},expression:"module.checkAll"}},[e._v("\n                                    "+e._s(t.name)+"\n                                ")]),e._v(" "),n("uxd-checkbox-group",{on:{change:function(n){return e.checkedSingleMenu(n,t)}},model:{value:t.moduleChecked,callback:function(n){e.$set(t,"moduleChecked",n)},expression:"module.moduleChecked"}},e._l(t.module,(function(e){return n("uxd-checkbox",{key:e,attrs:{label:e}})})),1)],1)})),0),e._v(" "),n("div",{staticClass:"element-modules__footer"},[n("uxd-checkbox",{on:{change:e.switchdAllMenu},model:{value:e.menuConfig.checkAll,callback:function(t){e.$set(e.menuConfig,"checkAll",t)},expression:"menuConfig.checkAll"}},[e._v("\n                                全选/全不选\n                            ")]),e._v(" "),e.moudleCount>e.limitMoudlesCount?n("uxd-button",{staticStyle:{"margin-left":"20px"},attrs:{size:"small"},on:{click:e.checkLimitMoudles}},[e._v("\n                                勾选前"+e._s(e.limitMoudlesCount)+"个数据\n                            ")]):e._e()],1),e._v(" "),n("uxd-button",{attrs:{slot:"reference",type:"text"},slot:"reference"},[e._v("\n                            "+e._s(e.menuConfig.menuButtonText)+"\n                            "),n("uxd-button",{attrs:{"is-icon":"",icon:"uxd-icon-arrow-down"}})],1)],1)],1)]),e._v(" "),n("uxd-button",{attrs:{type:"text",icon:"uxd-icon-replay"},on:{click:function(t){return t.stopPropagation(),e.resetCondition(t)}}},[e._v("\n                恢复默认\n            ")])],1)]),e._v(" "),e.isShowDetailSearch?e._e():n("div",{class:e.classPrefix+"__summary"}),e._v(" "),e.isShowDetailSearch?n("div",{class:e.classPrefix+"__detail"},[n("div",{class:e.classPrefix+"__detail--result-moudle"},[n("uxd-tabs",{on:{"tab-click":function(t){return e.handleSearchResult(!1)}},model:{value:e.searchMoudlesCondition,callback:function(t){e.searchMoudlesCondition=t},expression:"searchMoudlesCondition"}},e._l(e.resultMoudles,(function(e){return n("uxd-tab-pane",{key:e.indexName,attrs:{label:e.countName,name:e.niceName}})})),1)],1),e._v(" "),n("div",{class:e.classPrefix+"__detail--result-list"},[n("uxd-scrollbar",[e._l(e.resultList,(function(t){return[n("div",{key:t.indexid,staticClass:"result-item"},e._l(Object.keys(t),(function(a){return n("div",{key:t.indexid+"-"+a,staticClass:"result-item--content"},[n("p",{staticClass:"label"},[n("span",[e._v(e._s(a)+"：")])]),e._v(" "),n("uxd-tooltip",{staticClass:"item",attrs:{effect:"dark",content:t[a],placement:"right-start"}},[n("span",{staticClass:"value"},[e._v(e._s(t[a]))])])],1)})),0),e._v(" "),n("uxd-divider",{key:t.indexid})]}))],2),e._v(" "),n("div",{staticClass:"pagination"},[n("uxd-pagination",{staticClass:"custom-total",attrs:{"current-page":e.pageIndex,"page-sizes":[50,100,150,200],"page-size":e.pageSize,layout:"slot,prev, pager, next, jumper,sizes",total:e.pageTotal},on:{"size-change":e.handleSizeChange,"current-change":e.handleCurrentChange,"update:currentPage":function(t){e.pageIndex=t},"update:current-page":function(t){e.pageIndex=t}}},[n("div",{staticClass:"uxd-pagination__total-custom"},[e._v("\n                        共"+e._s(e.realTotal)+"条\n                    ")])])],1)],1)]):e._e()])},i=[],c=n("b85c"),o=n("5530"),u=n("c7eb"),s=n("1da1"),r=n("ade3"),l=(n("d81d"),n("a630"),n("3ca3"),n("14d9"),n("d3b7"),n("159b"),n("4de4"),n("a15b"),n("e9c4"),n("99af"),n("3c65"),n("b64b"),n("caad"),n("2532"),n("a434"),n("d232")),d=n("4f8d"),m=n("2062");n("af46");function h(e){return l["a"].get({url:d["e"].dataSourceList,params:e},{baseUrl:m["a"]})}function f(e){return l["a"].get({url:d["e"].modules,params:e},{baseUrl:m["a"]})}function p(e){return new Promise((function(e){e({status:200,msg:"成功",consumeTime:"71ms",data:[{indexName:"inf-basic-db",nickName:"数据库操作日志",count:100865},{indexName:"inf-remote",nickName:"远程控制日志",count:13821},{indexName:"inf-sip-metadata",nickName:"SIP元数据",count:9869},{indexName:"inf-account-detect",nickName:"口令账号检测",count:8057},{indexName:"inf-http-attack",nickName:"WEB渗透检测",count:5766},{indexName:"inf-hotthreat-virus-risk",nickName:"僵木蠕病检测",count:5538},{indexName:"inf-hotthreat-apt-risk",nickName:"APT威胁检测",count:5043},{indexName:"inf-snort",nickName:"网络入侵记录",count:2305},{indexName:"inf-black-emlbox-eml",nickName:"黑邮箱往来",count:2068},{indexName:"inf-http2-meta",nickName:"HTTP2元数据",count:867},{indexName:"inf-basic-ftp-login",nickName:"FTP登录日志",count:857},{indexName:"inf-http-alarm-risk",nickName:"HTTP文件传输",count:736},{indexName:"inf-http-alarm-risk1",nickName:"(恶)HTTP文件传输",count:736},{indexName:"inf-sign-http",nickName:"HTTP上线检测",count:664},{indexName:"inf-mining-risk",nickName:"挖矿行为检测",count:585},{indexName:"inf-maillogin",nickName:"邮箱登录日志",count:253},{indexName:"inf-attachment-risk",nickName:"木马附件",count:249},{indexName:"inf-scan-activity-warning",nickName:"端口扫描检测",count:240},{indexName:"inf-hotthreat-ransom-risk",nickName:"勒索行为检测",count:229},{indexName:"inf-eml-risk",nickName:"木马邮件",count:211},{indexName:"inf-black-emlbox-log",nickName:"黑邮箱登录",count:181},{indexName:"inf-black-http-alarm",nickName:"黑样本HTTP木马",count:158},{indexName:"inf-basic-samba-file",nickName:"SMB文件传输",count:132},{indexName:"inf-basic-samba-file1",nickName:"(恶)SMB文件传输",count:132},{indexName:"inf-klblpj",nickName:"暴力破解检测",count:124},{indexName:"inf-diameter-meta",nickName:"Diameter元数据",count:96},{indexName:"inf-web-file-download",nickName:"WEB文件下载日志",count:95},{indexName:"inf-black-attachment",nickName:"黑样本邮件附件",count:58},{indexName:"inf-ddos-warning",nickName:"拒绝服务检测",count:58},{indexName:"inf-eml-black-ip",nickName:"黑IP发邮",count:42},{indexName:"inf-fishingmail-detectresults",nickName:"钓鱼邮件",count:25},{indexName:"inf-sign-net",nickName:"木马通信检测",count:23},{indexName:"inf-black-http",nickName:"黑链接检测",count:17},{indexName:"inf-shell-cmd",nickName:"明文SHELL检测",count:12},{indexName:"inf-black-ip",nickName:"黑IP会话",count:8},{indexName:"inf-basic-samba-login",nickName:"SMB登录日志",count:4},{indexName:"inf-vpn",nickName:"VPN通联日志",count:2},{indexName:"inf-black-domain",nickName:"黑域名检测",count:0},{indexName:"inf-dns-scan",nickName:"DNS隧道检测",count:0},{indexName:"inf-ssl-detect",nickName:"SSL证书检测",count:0},{indexName:"inf-dns-hack-risk",nickName:"DNS劫持检测",count:0},{indexName:"inf-threat-detect-warning",nickName:"异常行为检测",count:0},{indexName:"inf-upload-file",nickName:"文件列表",count:0},{indexName:"ip-*",nickName:"IP会话",count:0},{indexName:"inf-http-metadata",nickName:"HTTP元数据",count:0},{indexName:"dns-*",nickName:"DNS解析",count:0},{indexName:"inf-basic-ftp-file",nickName:"FTP文件传输",count:0},{indexName:"inf-basic-ftp-file1",nickName:"(恶)FTP文件传输",count:0}]})}))}function x(e){return l["a"].post({url:d["e"].searchResult,params:e,headers:{"Content-Type":"application/x-www-form-urlencoded;charset=UTF-8"}},{baseUrl:m["a"]})}var k=n("ef44"),g=n("5fda"),N={components:{echartsBar:g["a"]},data:function(){var e,t,n=this;return t={options:Array.from({length:10}).map((function(e,t){return{key:"".concat(t),name:"item_".concat(t)}})),value:"",loading:!1,classPrefix:"onekey-search",isShowDetailSearch:!1,isShowSearchConfig:!1,dateConfig:{isReserve:!1,dateVisible:!1,dateButtonText:"最近30天",dateValue:null,dateOption:{onPick:function(e){var t=e.maxDate,a=e.minDate;n.pickMinDate=a,n.pickMaxDate=t},disabledDate:function(e){if(n.pickMinDate&&!n.pickMaxDate){var t=2592e6,a=n.pickMinDate.getTime()-t,i=n.pickMinDate.getTime()+t;return e.getTime()<a||e.getTime()>i}return!1},shortcuts:[{text:"最近30分钟",onClick:function(e){e.$emit("pick",Object(k["c"])(this.text))}},{text:"最近1小时",onClick:function(e){e.$emit("pick",Object(k["c"])(this.text))}},{text:"当天",onClick:function(e){e.$emit("pick",Object(k["c"])(this.text))}},{text:"最近24小时",onClick:function(e){e.$emit("pick",Object(k["c"])(this.text))}},{text:"最近7天",onClick:function(e){e.$emit("pick",Object(k["c"])(this.text))}},{text:"最近30天",onClick:function(e){e.$emit("pick",Object(k["c"])(this.text))}}]}},menuConfig:{checkAll:!0,modules:[],menuButtonText:"",menuVisible:!0},moudleSummaryChat:{title:{text:"全库数据",top:"16px",left:"10%",textStyle:{color:"#c2d4e9",fontSize:14}},xAxis:{data:[]},series:[{data:[]}],dataZoom:[{type:"slider",xAxisIndex:0,start:0,end:50}]},queryParam:{indexName:"",keywords:"",expression:"",sortDesc:!0,filterFields:[],sort:[{field:"@createtime",order:"desc"}],conditions:[]},queryMoudleParam:{},resultMoudles:[],resultList:[],pageIndex:1,pageSize:50,pageTotal:0,realTotal:0,limitTotal:1e4,searchMoudlesCondition:"全部",limitMoudlesCount:(null===(e=this.$route.meta)||void 0===e?void 0:e.limitMoudlesCount)||500,moduleCheckedLength:0},Object(r["a"])(t,"loading",!1),Object(r["a"])(t,"dataSource",""),Object(r["a"])(t,"dataSourceList",[]),Object(r["a"])(t,"dataSourceListPageIndex",1),Object(r["a"])(t,"dataSourceListPageSize",10),Object(r["a"])(t,"moudleCount",0),Object(r["a"])(t,"modules",[]),t},created:function(){this.initData()},watch:{"dateConfig.dateValue":{handler:function(e,t){this.setQueryParamDateRange(this.dateConfig.dateValue),this.changeButtonText()},immediate:!1},modules:{handler:function(e,t){this.formatModulesData(e)},immediate:!1},isShowDetailSearch:{handler:function(e){e&&this.$nextTick((function(){document.getElementsByClassName("uxd-pagination__jump")[0].childNodes[0].nodeValue="跳至"}))}}},methods:{loadMore:function(){var e=this;this.loading=!0,setTimeout((function(){for(var t=0;t<10;t++)e.options.push({key:e.options.length,name:"lazy item_".concat(e.options.length)});e.loading=!1}),1e3)},initData:function(){var e=this;return Object(s["a"])(Object(u["a"])().mark((function t(){return Object(u["a"])().wrap((function(t){while(1)switch(t.prev=t.next){case 0:return t.next=2,e.getSearchDataSource(!1);case 2:e.dataSourceList.length>0&&(e.dataSource=e.dataSourceList[0].id),e.getModuleList(),e.getEchartData();case 5:case"end":return t.stop()}}),t)})))()},getSearchDataSource:function(){var e=arguments,t=this;return Object(s["a"])(Object(u["a"])().mark((function n(){var a,i;return Object(u["a"])().wrap((function(n){while(1)switch(n.prev=n.next){case 0:return a=!(e.length>0&&void 0!==e[0])||e[0],t.loadingDataSource=!0,n.prev=2,0==a&&(t.dataSourceListPageIndex=1),n.next=6,h({datasource:"es",current:t.dataSourceListPageIndex,size:t.dataSourceListPageSize});case 6:i=n.sent,200==i.code&&(1==a?(i.content.data.forEach((function(e){t.dataSourceList.push(e)})),t.dataSourceListPageIndex++):t.dataSourceList=i.content.data);case 8:return n.prev=8,t.loadingDataSource=!1,n.finish(8);case 11:case"end":return n.stop()}}),n,null,[[2,,8,11]])})))()},setQueryParamDateRange:function(e){this.queryParam.conditions=this.queryParam.conditions.filter((function(e){return"@createtime"===e.field})),this.queryParam.conditions.push({field:"@createtime",op:"range",value:e.join(" - ")})},handleSearchResult:function(){var e=arguments,t=this;return Object(s["a"])(Object(u["a"])().mark((function n(){var a,i,c,s;return Object(u["a"])().wrap((function(n){while(1)switch(n.prev=n.next){case 0:if(a=!(e.length>0&&void 0!==e[0])||e[0],""!=t.queryParam.keywords){n.next=4;break}return t.$message({message:"请输入查询条件！",type:"error"}),n.abrupt("return");case 4:if(!(t.moduleCheckedLength>t.limitMoudlesCount)){n.next=7;break}return t.$message({message:"最多支持".concat(t.limitMoudlesCount,"个模块查询！"),type:"error"}),n.abrupt("return");case 7:return t.isShowDetailSearch=!0,t.isShowSearchConfig=!0,0==a&&("全部"==t.searchMoudlesCondition?t.queryParam.indexName=t.menuConfig.modules[0].moduleChecked.join(","):t.queryParam.indexName=t.searchMoudlesCondition),t.loading=!0,n.prev=11,n.next=14,x({dataSourceId:t.dataSource,params:JSON.stringify(t.queryParam),page:t.pageIndex,limit:t.pageSize});case 14:i=n.sent,200==i.status&&(a&&(c=i.model.map((function(e){return Object(o["a"])(Object(o["a"])({},e),{},{countName:"".concat(e.niceName,"（").concat(e.count,"）")})})),s=c.reduce((function(e,t){return e+t.count}),0),c.unshift({indexName:"",count:s,niceName:"全部",countName:"全部（".concat(s,"）"),urlAddress:"",pageId:""}),t.resultMoudles=c,t.searchMoudlesCondition=c[0].niceName),t.resultList=i.data.map((function(e){return Object(o["a"])({indexid:e.indexid,indexName:e.indexName,createtime:e.createtime},e.niceYuan)})),t.realTotal=i.total,t.pageTotal=t.realTotal>=t.limitTotal?t.limitTotal:t.realTotal),n.next=23;break;case 18:n.prev=18,n.t0=n["catch"](11),t.resultList=[],t.realTotal=0,t.pageTotal=0;case 23:return n.prev=23,t.loading=!1,n.finish(23);case 26:case"end":return n.stop()}}),n,null,[[11,18,23,26]])})))()},handleShowSearchConfig:function(){this.isShowSearchConfig=!this.isShowSearchConfig},changeButtonText:function(){this.dateConfig.dateButtonText=this.dateConfig.dateValue?this.dateConfig.dateValue.join(" - "):"最近30天"},changeDateVisible:function(){this.$refs["dateRange"].focus(),this.$refs["menuPopover"].doClose(),this.dateConfig.dateVisible=!0},formatModulesData:function(e){var t=this,n=[];e.forEach((function(e){var a,i={name:e.NAME,checkAll:!1,module:[],moduleChecked:[],moduleMapping:{},moduleSetting:{}},o=0,u=Object(c["a"])(e.NODE_DIR);try{for(u.s();!(a=u.n()).done;){var s=a.value;i.module.push(s.NAME),i.moduleMapping[s.NAME]=s.INDEX_NAME,i.moduleSetting[s.INDEX_NAME]=s.NAME,o++,o<=t.limitMoudlesCount&&i.moduleChecked.push(s.NAME)}}catch(r){u.e(r)}finally{u.f()}i.checkAll=i.module.length===i.moduleChecked.length,n.push(i)})),this.menuConfig.modules=n,this.calcMenuButtonText()},checkedAllMenu:function(e,t){t["moduleChecked"]=e?t.module:[],this.calcMenuButtonText()},checkedSingleMenu:function(e,t){var n=e.length,a=t.module.length;t.checkAll=n===a,this.calcMenuButtonText()},calcMenuButtonText:function(){var e=0,t=0;this.menuConfig.modules.forEach((function(n){e+=n.module.length,t+=n.moduleChecked.length})),this.menuConfig.checkAll=e==t,this.menuConfig.menuButtonText="".concat(t,"个已选"),this.moduleCheckedLength=t,this.menuConfig.modules.length>0&&(this.queryParam.indexName=this.menuConfig.modules[0].moduleChecked.join(","))},switchdAllMenu:function(e){this.menuConfig.modules.forEach((function(t){t["moduleChecked"]=e?t.module:[],t.checkAll=!!e})),this.calcMenuButtonText()},setModulesChecked:function(e){this.switchdAllMenu(!0);var t=JSON.parse(JSON.stringify(this.menuConfig.modules));t.forEach((function(t){var n=Object.keys(t.moduleSetting);n.forEach((function(n){if(!e.includes(n)){var a=t.moduleSetting[n],i=t.moduleChecked.indexOf(a);t.moduleChecked.splice(i,1),t.module}})),t.checkAll=t.module.length===t.moduleChecked.length})),this.menuConfig.modules=t,this.calcMenuButtonText()},setTimeRangeChecked:function(e){this.dateConfig.dateValue=e},getSearchSetting:function(){var e=[];return this.menuConfig.modules.forEach((function(t){var n=t["moduleChecked"]||[];n.length&&n.forEach((function(n){e.push(t["moduleMapping"][n])}))})),{dateValue:this.dateConfig.dateValue,indexName:e}},resetCondition:function(){this.dateConfig.dateValue=null,this.switchdAllMenu(!0)},getModuleList:function(){var e=this;return Object(s["a"])(Object(u["a"])().mark((function t(){var n,a;return Object(u["a"])().wrap((function(t){while(1)switch(t.prev=t.next){case 0:return t.next=2,f({dataSourceId:e.dataSource});case 2:n=t.sent,200==n.code&&(a=n.content.data,e.modules=[a],e.moudleCount=a.NODE_DIR.length);case 4:case"end":return t.stop()}}),t)})))()},getEchartData:function(){var e=this;return Object(s["a"])(Object(u["a"])().mark((function t(){var n,a;return Object(u["a"])().wrap((function(t){while(1)switch(t.prev=t.next){case 0:return n={special:"home",params:JSON.stringify({indexName:e.queryParam.indexName,keywords:e.queryParam.keywords,sortDesc:!0,conditions:[]})},t.next=3,p(n);case 3:a=t.sent,200==a.status&&(e.moudleSummaryChat.xAxis.data=a.data.map((function(e){return e.nickName})),e.moudleSummaryChat.series[0].data=a.data.map((function(e){return e.count})),e.moudleSummaryChat.dataZoom[0].end=1500/e.moudleSummaryChat.xAxis.data.length);case 5:case"end":return t.stop()}}),t)})))()},handleSizeChange:function(e){this.handleSearchResult(!1)},handleCurrentChange:function(e){this.handleSearchResult(!1)},jumpFirstPage:function(){this.handleCurrentChange(1)},jumpLastPage:function(){this.handleCurrentChange(Math.ceil(this.pageTotal/this.pageSize))},checkLimitMoudles:function(){this.menuConfig.checkAll=!1,this.formatModulesData(this.modules)}}},C=N,b=(n("087c"),n("2877")),v=Object(b["a"])(C,a,i,!1,null,"5f351fdf",null);t["default"]=v.exports}}]);