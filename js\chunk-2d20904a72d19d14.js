(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-2d20904a"],{a6ed:function(e,t,n){"use strict";n.r(t);var i=function(){var e=this,t=e.$createElement,n=e._self._c||t;return n("edr-table",{directives:[{name:"loading",rawName:"v-loading",value:e.loading,expression:"loading"}],ref:"tableRef",attrs:{tableFieldList:e.tableFieldList,api:e.terminalListApi,params:e.queryList,selection:50,selectable:e.selectable,optHeader:!0},scopedSlots:e._u([{key:"state",fn:function(t){var i=t.row;return[n("span",{class:1!=i.state?"state-danger":null},[e._v("\n            "+e._s(e.STATUS_MAP.get(i.state))+"\n        ")])]}},{key:"type",fn:function(t){var n=t.row;return[e._v("\n        "+e._s(e.GATHER_MAP.get(n.type))+"\n    ")]}},{key:"commType",fn:function(t){var n=t.row;return[e._v("\n        "+e._s(e.COMM_MAP.get(n.commType)||"-")+"\n    ")]}}])},[n("template",{slot:"search-header-form"},[n("uxd-form-item",{attrs:{label:"设备类型"}},[n("cascader",{attrs:{list:e.queryList.nodeType,value:"string",popperClass:"table-search-header"},on:{"update:list":function(t){return e.$set(e.queryList,"nodeType",t)}}})],1),e._v(" "),n("uxd-form-item",{attrs:{label:"状态"}},[n("uxd-select",{attrs:{placeholder:"请选择状态",clearable:""},model:{value:e.queryList.state,callback:function(t){e.$set(e.queryList,"state",t)},expression:"queryList.state"}},e._l(e.STATUS_MAP,(function(e){var t=e[0],i=e[1];return n("uxd-option",{key:t,attrs:{value:t,label:i}})})),1)],1)],1),e._v(" "),n("template",{slot:"search-header-opt"},[n("span",{staticClass:"confirm-btn search-btn",on:{click:e.search}},[e._v("\n            搜索\n        ")]),e._v(" "),n("span",{staticClass:"cancel-btn reset-btn",on:{click:e.reset}},[e._v("\n            重置\n        ")])]),e._v(" "),n("template",{slot:"opt-header"},[n("span",{staticClass:"cancel-btn",on:{click:e.clockSync}},[e._v("时钟同步")]),e._v(" "),n("span",{staticClass:"cancel-btn",on:{click:e.configFrequency}},[e._v("\n            配置终端采集频率\n        ")]),e._v(" "),n("span",{staticClass:"cancel-btn",on:{click:e.communicationIP}},[e._v("\n            配置终端通信IP\n        ")]),e._v(" "),n("span",{staticClass:"cancel-btn",on:{click:e.handleSwitch}},[e._v("带内带外切换")])]),e._v(" "),e._v(" "),e._v(" "),e._v(" "),n("uxd-table-column",{attrs:{width:"100px",label:"操作"},scopedSlots:e._u([{key:"default",fn:function(t){var i=t.row;return[n("uxd-popconfirm",{attrs:{icon:"uxd-icon-info","icon-color":"red",title:"您确定要"+(1===i.state?"停止":"启动")+"吗？"},on:{confirm:function(t){return e.handleState(i)}}},[n("span",{staticClass:"operation",attrs:{slot:"reference"},slot:"reference"},[e._v("\n                    "+e._s(1===i.state?"停止":"启动")+"\n                ")])])]}}])}),e._v(" "),n("common-add-edit-dialog",{attrs:{visible:e.dialog.configFrequency.visible,title:e.dialog.configFrequency.title,data:e.dialog.configFrequency.data,config:e.dialog.configFrequency.config},on:{"update:visible":function(t){return e.$set(e.dialog.configFrequency,"visible",t)},success:e.configFrequencySuccess}}),e._v(" "),n("common-add-edit-dialog",{attrs:{visible:e.dialog.communicationIP.visible,title:e.dialog.communicationIP.title,data:e.dialog.communicationIP.data,config:e.dialog.communicationIP.config},on:{"update:visible":function(t){return e.$set(e.dialog.communicationIP,"visible",t)},success:e.communicationIPSuccess}})],2)},s=[],a=(n("d3b7"),n("ac1f"),n("841c"),n("d81d"),n("00b4"),n("d9e2"),n("d232")),c=n("4f8d");function o(e){return a["a"].get({url:c["d"].terminalList,params:e})}function r(e){return a["a"].post({url:c["d"].control,params:e})}function l(e){return a["a"].post({url:c["d"].clockSync,params:e})}function u(e){return a["a"].post({url:c["d"].configFrequency,params:e})}function d(e){return a["a"].post({url:c["d"].communicationIP,params:e})}function f(e){return a["a"].post({url:c["d"].switchComm,params:e})}n("4ec9"),n("3ca3"),n("ddb0");var m=new Map([[0,"流量采集"],[1,"终端采集"],[2,"实装装备信息采集"]]),p=new Map([[0,"已停止"],[1,"运行中"],[-1,"异常"]]),g=new Map([[0,"带外"],[1,"带内"]]),v={data:function(){return{terminalListApi:o,STATUS_MAP:p,GATHER_MAP:m,COMM_MAP:g,queryList:{nodeType:"",state:""},loading:!1,dialog:{configFrequency:{visible:!1,title:"配置终端采集频率",data:{frequencyNumber:1,ids:[]},config:{props:[{prop:"frequencyNumber",label:"采集频率",type:"Number",min:1,rules:[{required:!0,message:"请输入采集频率",trigger:"blur"}]}],addApi:u}},communicationIP:{visible:!1,title:"配置终端通信IP",data:{serverIp:"",ids:[]},config:{props:[{prop:"serverIp",label:"通信IP",rules:[{required:!0,message:"请输入通信IP",trigger:"blur"},{validator:this.validateIp,trigger:"blur"}]}],addApi:d}}}}},computed:{tableFieldList:function(){var e,t,n;return(null===(e=this.$store.state.system)||void 0===e?void 0:null===(t=e.config)||void 0===t?void 0:null===(n=t.table)||void 0===n?void 0:n.terminal)||[]}},methods:{handleState:function(e){var t=this,n=e.nodeId,i=e.state,s=e.sceneId,a=e.type;this.loading=!0,r({nodeId:n,control:0==i?1:0,sceneId:s,type:a}).then((function(){t.$message.success("操作成功"),t.search()})).finally((function(){t.loading=!1}))},reset:function(){this.queryList.nodeType="",this.queryList.state="",this.search()},search:function(){this.$refs.tableRef.getDataList(this.queryList)},clockSync:function(){var e=this,t=this.$refs.tableRef.multipleSelection;0!=t.length?l(t.map((function(e){return{nodeId:e.nodeId,sceneId:e.sceneId}}))).then((function(t){200==t.code&&(e.$message({message:"时钟同步成功！",type:"success"}),e.$refs.tableRef.clearSelection())})):this.$message({message:"请选择勾选项！",type:"info"})},selectable:function(e){return 1==e.type},configFrequency:function(){var e=this.$refs.tableRef.multipleSelection;0!=e.length?(this.dialog.configFrequency.data.ids=e.map((function(e){return e.id})),this.dialog.configFrequency.visible=!0):this.$message({message:"请选择勾选项！",type:"info"})},communicationIP:function(){var e=this.$refs.tableRef.multipleSelection;0!=e.length?(this.dialog.communicationIP.data.ids=e.map((function(e){return e.id})),this.dialog.communicationIP.visible=!0):this.$message({message:"请选择勾选项！",type:"info"})},handleSwitch:function(){var e=this,t=this.$route.meta.switchCountLimit||20,n=this.$refs.tableRef.multipleSelection;0!=n.length?n.length>t?this.$message({message:"最多支持切换".concat(t,"条数据!"),type:"info"}):f({ids:n.map((function(e){return e.id}))}).then((function(t){200!=t.code&&200!=t.status||(e.$message({message:"切换成功！",type:"success"}),e.$refs.tableRef.clearSelection(),e.search())})):this.$message({message:"请选择勾选项！",type:"info"})},validateIp:function(e,t,n){var i=/^(25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)\.(25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)\.(25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)\.(25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)$/;i.test(t)?n():n(new Error("请输入有效的IP地址"))},configFrequencySuccess:function(){this.$message({message:"配置终端采集频率成功!",type:"success"}),this.search()},communicationIPSuccess:function(){this.$message({message:"配置终端通信IP成功!",type:"success"}),this.search()}}},h=v,b=n("2877"),y=Object(b["a"])(h,i,s,!1,null,null,null);t["default"]=y.exports}}]);