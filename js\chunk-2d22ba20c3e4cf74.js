(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-2d22ba20"],{f099:function(t,n,e){"use strict";e.d(n,"c",(function(){return i})),e.d(n,"b",(function(){return p})),e.d(n,"h",(function(){return d})),e.d(n,"i",(function(){return f})),e.d(n,"a",(function(){return l})),e.d(n,"g",(function(){return m})),e.d(n,"d",(function(){return w})),e.d(n,"e",(function(){return v})),e.d(n,"f",(function(){return C}));var r=e("c7eb"),a=e("1da1"),c=(e("99af"),e("d3b7"),e("25f0"),e("3ca3"),e("ddb0"),e("9861"),e("4de4"),e("b0c0"),e("d232")),o=e("4f8d"),u=e("2062"),s=!1;function i(t){return c["a"].post({url:"".concat(o["c"].getComponentList,"?").concat(new URLSearchParams(t).toString()),params:{no_page:!0}},{baseUrl:u["b"]})}function p(t){return b.apply(this,arguments)}function b(){return b=Object(a["a"])(Object(r["a"])().mark((function t(n){return Object(r["a"])().wrap((function(t){while(1)switch(t.prev=t.next){case 0:return console.log(n,"getComponentHostListApi"),t.abrupt("return",c["a"].post({url:o["c"].getComponentHostList,params:n},{baseUrl:u["b"]}));case 2:case"end":return t.stop()}}),t)}))),b.apply(this,arguments)}function d(t){return c["a"].post({url:o["c"].startComponentApi,params:t},{baseUrl:u["b"]})}function f(t){return c["a"].post({url:o["c"].stopComponentApi,params:t},{baseUrl:u["b"]})}function l(t){return c["a"].post({url:o["c"].deleteComponentApi,params:t},{baseUrl:u["b"]})}function m(t){return c["a"].post({url:o["c"].restartComponentApi,params:t},{baseUrl:u["b"]})}function w(t){return h.apply(this,arguments)}function h(){return h=Object(a["a"])(Object(r["a"])().mark((function t(n){var e,a;return Object(r["a"])().wrap((function(t){while(1)switch(t.prev=t.next){case 0:if(!s){t.next=4;break}return t.abrupt("return",new Promise((function(t){t({code:200,data:[{serviceName:"test",id:123}]})})));case 4:return e=n.type,delete n.type,t.next=8,c["a"].post({url:"".concat(o["c"].getInstallService,"?").concat(new URLSearchParams({type:e}).toString()),params:n},{baseUrl:u["b"]});case 8:if(a=t.sent,200!=a.code){t.next=11;break}return t.abrupt("return",{code:200,data:a.data.filter((function(t){return 0==t.installed&&t.serviceName.toLowerCase().indexOf(n.name)>-1}))});case 11:return t.abrupt("return",a);case 12:case"end":return t.stop()}}),t)}))),h.apply(this,arguments)}var g=0,U=[10,20,50,70,100];function v(t){return s?new Promise((function(t){g++,g>=U.length&&(g=0),t({code:200,data:{commandProgress:U[g]}})})):c["a"].post({url:o["c"].getInstallServiceProgress,params:t},{baseUrl:u["b"]})}function C(t){return s?new Promise((function(t){t({code:200,data:123})})):c["a"].post({url:o["c"].installService,params:t},{baseUrl:u["b"]})}}}]);