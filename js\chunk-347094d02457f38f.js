(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-347094d0"],{3042:function(t,e,a){"use strict";var n=a("e1fa"),i=a.n(n);i.a},"738a":function(t,e,a){"use strict";a.r(e);var n=function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("div",{staticClass:"data-dev-page"},[a("edr-table",{ref:"tableRef",attrs:{tableFieldList:t.tableFieldList,api:t.dataDevListApi,params:t.queryList},scopedSlots:t._u([{key:"runStatus",fn:function(e){var n=e.row;return[t.RUN_STATUS_MAP.get(n.runStatus)?a("uxd-tag",{staticClass:"tag--status",attrs:{type:t.RUN_STATUS_MAP.get(n.runStatus).theme}},[t._v("\n                "+t._s(t.RUN_STATUS_MAP.get(n.runStatus).label)+"\n            ")]):t._e()]}},{key:"component",fn:function(e){var a=e.row;return[t._v("\n            "+t._s(void 0!==t.componentOptions.find((function(t){return t.value==a.component}))?t.componentOptions.find((function(t){return t.value==a.component})).label:"")+"\n        ")]}}])},[t._v(" "),t._v(" "),a("template",{slot:"search-header-form"},[a("uxd-form",{ref:"formRef",attrs:{model:t.queryList,"label-suffix":"：",inline:""}},[a("uxd-form-item",{attrs:{label:"任务名称"}},[a("uxd-input",{attrs:{placeholder:"请输入任务名称"},model:{value:t.queryList.taskName,callback:function(e){t.$set(t.queryList,"taskName",e)},expression:"queryList.taskName"}})],1),t._v(" "),a("span",{staticClass:"confirm-btn search-btn",on:{click:t.search}},[t._v("\n                    搜索\n                ")]),t._v(" "),a("span",{staticClass:"cancel-btn reset-btn",on:{click:t.reset}},[t._v("\n                    重置\n                ")])],1),t._v(" "),a("uxd-divider"),t._v(" "),a("div",{staticClass:"search-quick"},[a("p",[a("span",{staticClass:"confirm-btn",on:{click:t.handleAdd}},[t._v("\n                        新增任务\n                    ")])]),t._v(" "),a("uxd-checkbox-group",{staticClass:"checkbox-group--status",attrs:{size:"small"},on:{change:t.search},model:{value:t.queryList.runStatus,callback:function(e){t.$set(t.queryList,"runStatus",e)},expression:"queryList.runStatus"}},t._l(Array.from(t.RUN_STATUS_MAP.entries()),(function(e){var n=e[0],i=e[1];return a("uxd-checkbox-button",{key:n,class:"status-"+i.theme,attrs:{general:"",label:n}},[t._v("\n                        "+t._s(i.label)+"\n                    ")])})),1)],1)],1),t._v(" "),a("uxd-table-column",{attrs:{width:"220px",label:"操作"},scopedSlots:t._u([{key:"default",fn:function(e){var n=e.row;return[a("uxd-button",{attrs:{type:"text",primary:""},on:{click:function(e){return t.handleDetail(n)}}},[t._v("\n                    详情\n                ")]),t._v(" "),a("uxd-button",{attrs:{type:"text",primary:""},on:{click:function(e){return t.handleRunStop(n)}}},[t._v("\n                    "+t._s(1==n.runStatus?"停止":"运行")+"\n                ")]),t._v(" "),a("uxd-button",{attrs:{type:"text",primary:""},on:{click:function(e){return t.handleEdit(n)}}},[t._v("\n                    编辑\n                ")]),t._v(" "),a("uxd-button",{attrs:{type:"text",primary:""},on:{click:function(e){return t.handleDelete(n)}}},[t._v("\n                    删除\n                ")])]}}])}),t._v(" "),a("detail-dialog",{attrs:{visible:t.dialog.detailDialog.visible,data:t.dialog.detailDialog.data},on:{"update:visible":function(e){return t.$set(t.dialog.detailDialog,"visible",e)}}}),t._v(" "),a("common-add-edit-dialog",{attrs:{visible:t.dialog.addEditDialog.visible,title:t.dialog.addEditDialog.title,data:t.dialog.addEditDialog.data,config:t.dialog.addEditDialog.config},on:{"update:visible":function(e){return t.$set(t.dialog.addEditDialog,"visible",e)}}})],2)],1)},i=[],o=a("c7eb"),s=a("1da1"),l=(a("7db0"),a("d3b7"),a("d81d"),a("ac1f"),a("841c"),a("b0c0"),a("f099"));a("d232"),a("4f8d");function r(t){return new Promise((function(t){t({status:200,data:[{taskName:"测试任务1",component:1,storeTable:"存储表1",dataName:"数据名称1",runStatus:1,durationTime:"1h",createTime:"2025-02-25 16:32:00"},{taskName:"测试任务2",component:2,storeTable:"存储表2",dataName:"数据名称2",runStatus:0,durationTime:"2h",createTime:"2025-02-25 16:42:00"}],realTotal:2})}))}function u(t){return new Promise((function(t){t({status:200})}))}function d(t){return new Promise((function(t){t({status:200})}))}a("4ec9"),a("3ca3"),a("ddb0");var c=new Map([[0,{label:"未开始",theme:"info"}],[1,{label:"进行中",theme:""}],[2,{label:"暂停",theme:"warning"}],[3,{label:"完成",theme:"success"}]]),f=function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("uxd-dialog",{attrs:{title:"运行日志",visible:t.visible,width:"450px","before-close":t.handleClose,"append-to-body":""},on:{"update:visible":function(e){t.visible=e}}},[a("div",{staticClass:"log-list"},t._l(t.logList,(function(e,n){return a("p",{key:n,staticClass:"log"},[t._v("\n      "+t._s(n)+"：\n      "),t._v(" "),a("span",[t._v(t._s(e))])])})),0),t._v(" "),a("span",{staticClass:"dialog-footer",attrs:{slot:"footer"},slot:"footer"},[a("span",{staticClass:"cancel-btn",on:{click:t.handleClose}},[t._v("取消")])])])},p=[],v={props:{visible:{type:Boolean,default:!1},data:{type:Object,default:function(){return{}}}},data:function(){return{loading:!1,logList:[]}},watch:{visible:{handler:function(t){t&&(this.logList=["2024-12-03 16:39:00 任务启动...","2024-12-03 16:39:01 读取数据源xxx","2024-12-03 16:39:30 任务终止"])}}},methods:{handleClose:function(){this.$emit("update:visible",!1)},handleConfirm:function(){this.$emit("update:visible",!1)}}},m=v,b=a("2877"),h=Object(b["a"])(m,f,p,!1,null,null,null),g=h.exports,_={components:{DetailDialog:g},computed:{tableFieldList:function(){var t,e,a,n=(null===(t=this.$store.state.system)||void 0===t?void 0:null===(e=t.config)||void 0===e?void 0:null===(a=e.table)||void 0===a?void 0:a.dataDev)||[],i=n.find((function(t){return"component"==t.prop}));return i.remoteMap=this.getComponentOptions,i.map=[],n}},created:function(){this.getComponentOptions()},data:function(){var t,e,a;return{queryList:{taskName:"",runStatus:[]},dataDevListApi:r,RUN_STATUS_MAP:c,dialog:{detailDialog:{visible:!1,data:{}},addEditDialog:{visible:!1,title:"新增任务",data:{},config:{props:(null===(t=this.$store.state.system)||void 0===t?void 0:null===(e=t.config)||void 0===e?void 0:null===(a=e.table)||void 0===a?void 0:a.dataDev)||[],addApi:u,editApi:d}}},componentList:[],componentOptions:[]}},methods:{reset:function(){this.queryList={taskName:"",runStatus:[]},this.search()},search:function(){this.getComponentOptions(),this.$refs.tableRef.getDataList(this.queryList)},handleAdd:function(){this.dialog.addEditDialog.title="新增任务",this.dialog.addEditDialog.visible=!0,this.dialog.addEditDialog.data={}},handleEdit:function(t){this.dialog.addEditDialog.title="新增任务",this.dialog.addEditDialog.visible=!0,this.dialog.addEditDialog.data=t},handleRunStop:function(t){var e=this,a=1==t.runStatus?"停止":"运行";this.$msgbox({message:"确定".concat(a,"此任务？"),confirmButtonText:a,cancelButtonText:"取消",showCancelButton:!0,showConfirmoButton:!0}).then((function(){e.search()}))},handleDelete:function(t){var e=this;this.$msgbox({message:"确定删除此任务？",confirmButtonText:"删除",cancelButtonText:"取消",showCancelButton:!0,showConfirmoButton:!0,confirmButtonClass:"uxd-button--danger"}).then((function(){e.search()}))},handleDetail:function(t){this.dialog.detailDialog.visible=!0},getComponentOptions:function(t){var e=this;return Object(s["a"])(Object(o["a"])().mark((function t(){var a,n;return Object(o["a"])().wrap((function(t){while(1)switch(t.prev=t.next){case 0:return t.next=2,Object(l["c"])();case 2:if(a=t.sent,!a||200!=a.status){t.next=7;break}return n=a.data.map((function(t){return{label:t.name,value:t.id}})),e.componentOptions=n,t.abrupt("return",n);case 7:case"end":return t.stop()}}),t)})))()}}},x=_,k=(a("3042"),Object(b["a"])(x,n,i,!1,null,"f1b99f32",null));e["default"]=k.exports},e1fa:function(t,e,a){}}]);