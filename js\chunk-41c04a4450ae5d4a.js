(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-41c04a44"],{"228c":function(e,t,i){"use strict";i.r(t);var r=function(){var e=this,t=e.$createElement,i=e._self._c||t;return i("edr-table",{ref:"tableRef",attrs:{tableFieldList:e.tableFieldList,api:e.strategyListApi,params:e.queryList},scopedSlots:e._u([{key:"strategyType",fn:function(t){var i=t.row;return[e._v("\n        "+e._s(e.STRATEGY_MAP.get(i.strategyType))+"\n    ")]}},{key:"source",fn:function(t){var i=t.row;return[e._v("\n        "+e._s(e.SOURCE_MAP.get(i.source))+"\n    ")]}},{key:"state",fn:function(t){var r=t.row;return[i("span",{class:1!=r.state?"state-danger":null},[e._v("\n            "+e._s(e.STATUS_MAP.get(r.state))+"\n        ")])]}},{key:"execDateType",fn:function(t){var r=t.row;return[i("span",[e._v("\n            "+e._s(e.execDateType_Map.get(r.execDateType))+"\n        ")])]}}])},[i("template",{slot:"search-header-form"},[i("uxd-form-item",{attrs:{label:"策略名称"}},[i("uxd-input",{attrs:{placeholder:"请输入策略名称"},model:{value:e.queryList.name,callback:function(t){e.$set(e.queryList,"name",t)},expression:"queryList.name"}})],1),e._v(" "),i("uxd-form-item",{attrs:{label:"设备类型"}},[i("cascader",{attrs:{list:e.queryList.deviceType,popperClass:"table-search-header"},on:{"update:list":function(t){return e.$set(e.queryList,"deviceType",t)}}})],1)],1),e._v(" "),i("template",{slot:"search-header-opt"},[i("span",{staticClass:"confirm-btn search-btn",on:{click:e.search}},[e._v("\n            搜索\n        ")]),e._v(" "),i("span",{staticClass:"cancel-btn reset-btn",on:{click:e.reset}},[e._v("\n            重置\n        ")])]),e._v(" "),i("template",{slot:"opt-header"},[i("span",{staticClass:"confirm-btn",on:{click:function(t){return e.openFormDialog()}}},[e._v("\n            新增\n        ")])]),e._v(" "),e._v(" "),e._v(" "),e._v(" "),e._v(" "),i("uxd-table-column",{attrs:{width:"160px",label:"操作"},scopedSlots:e._u([{key:"default",fn:function(t){var r=t.row;return[i("span",{class:["operation",0===r.source||2==r.strategyType?"disabled":null],on:{click:function(t){return e.openFormDialog(r)}}},[e._v("\n                编辑\n            ")]),e._v(" "),i("span",{class:["operation",0===r.source||2==r.strategyType?"disabled":null],on:{click:function(t){return e.handleDelete(r)}}},[e._v("\n                删除\n            ")]),e._v(" "),i("span",{class:["operation",0===r.source||2==r.strategyType?"disabled":null],attrs:{slot:"reference"},on:{click:function(t){return e.handleState(r)}},slot:"reference"},[e._v("\n                "+e._s(1===r.state?"停用":"启用")+"\n            ")])]}}])}),e._v(" "),i("form-dialog",{attrs:{visible:e.dialog.visible},on:{"update:visible":function(t){return e.$set(e.dialog,"visible",t)},refresh:e.search},model:{value:e.dialog.data,callback:function(t){e.$set(e.dialog,"data",t)},expression:"dialog.data"}})],2)},s=[],a=(i("ac1f"),i("841c"),i("b0c0"),i("d3b7"),i("d232")),n=i("4f8d");function c(e){return a["a"].get({url:n["d"].strategy,params:e})}function u(e){return a["a"].post({url:n["d"].deleteStrategy,params:e})}function l(e){return a["a"].uploadFile({url:n["d"].addStrategy},e)}function o(e){return a["a"].post({url:n["d"].updateStrategy,params:e})}function d(e){return a["a"].post({url:n["d"].startOrStopStrategy,params:e})}function p(e){return a["a"].get({url:n["d"].scripts,params:e})}var f=i("3dd8"),m=i("7b54"),v=function(){var e=this,t=e.$createElement,i=e._self._c||t;return i("uxd-dialog",{attrs:{title:e.queryList.id?"编辑采集策略":"新增采集策略",visible:e.visible,width:"557px","before-close":e.close,"append-to-body":""},on:{"update:visible":function(t){e.visible=t}}},[i("uxd-form",{ref:"formRef",attrs:{"label-width":"auto",model:e.queryList,rules:e.formRules,"label-suffix":"："}},[i("uxd-form-item",{attrs:{label:"策略名称",prop:"describe"}},[i("uxd-input",{attrs:{placeholder:"请输入策略名称"},model:{value:e.queryList.describe,callback:function(t){e.$set(e.queryList,"describe",t)},expression:"queryList.describe"}})],1),e._v(" "),i("uxd-form-item",{attrs:{label:"设备类型",prop:"deviceId"}},[i("cascader",{ref:"cascaderRef",attrs:{list:e.queryList.deviceId,multiple:"",popperClass:"dialog-form"},on:{"update:list":function(t){return e.$set(e.queryList,"deviceId",t)}}})],1),e._v(" "),e.queryList.id?e._e():i("uxd-form-item",{attrs:{label:"采集脚本",prop:"scriptId"}},[i("uxd-select",{staticClass:"script-dic-select",attrs:{placeholder:"请选择采集脚本"},on:{change:e.scriptChange},model:{value:e.queryList.scriptId,callback:function(t){e.$set(e.queryList,"scriptId",t)},expression:"queryList.scriptId"}},e._l(e.scriptList,(function(e){return i("uxd-option",{key:e.id,attrs:{label:e.scriptName,value:e.id}})})),1)],1),e._v(" "),e.queryList.id?e._e():i("uxd-form-item",{attrs:{label:"脚本名称",prop:"scriptPath"}},[i("uxd-input",{attrs:{placeholder:"请输入脚本名称",readonly:""},model:{value:e.queryList.scriptPath,callback:function(t){e.$set(e.queryList,"scriptPath",t)},expression:"queryList.scriptPath"}})],1),e._v(" "),i("uxd-form-item",{attrs:{label:"执行命令",prop:"execCommand"}},[i("uxd-input",{attrs:{placeholder:"请输入执行命令"},model:{value:e.queryList.execCommand,callback:function(t){e.$set(e.queryList,"execCommand",t)},expression:"queryList.execCommand"}})],1),e._v(" "),i("uxd-form-item",{attrs:{label:"执行周期",prop:"execCycle"}},[i("uxd-input-number",{staticStyle:{width:"250px"},attrs:{min:1,max:"M"==e.queryList.execDateType?59:23,precision:0,placeholder:"请输入执行周期（Quartz）"},model:{value:e.queryList.execCycle,callback:function(t){e.$set(e.queryList,"execCycle",t)},expression:"queryList.execCycle"}}),e._v(" "),i("uxd-radio-group",{staticStyle:{"margin-left":"16px"},attrs:{slow:!1},model:{value:e.queryList.execDateType,callback:function(t){e.$set(e.queryList,"execDateType",t)},expression:"queryList.execDateType"}},[i("uxd-radio-button",{attrs:{label:"H"}},[e._v("小时")]),e._v(" "),i("uxd-radio-button",{attrs:{label:"M"}},[e._v("分钟")])],1)],1)],1),e._v(" "),i("span",{staticClass:"dialog-footer",attrs:{slot:"footer"},slot:"footer"},[i("span",{staticClass:"cancel-btn",on:{click:e.close}},[e._v("取消")]),e._v(" "),i("uxd-button",{staticClass:"confirm-btn",attrs:{loading:e.loading},on:{click:e.confirm}},[e._v("\n            确认\n        ")])],1)],1)},y=[],h=i("c7eb"),b=i("1da1"),g=i("3835"),x=i("5530"),L=(i("b64b"),i("caad"),i("2532"),i("a15b"),i("4de4"),i("159b"),i("14d9"),i("2f62")),q=i("ed08"),_=i("ef44"),T=i("f656"),w={props:{visible:{type:Boolean,default:!1},value:{type:Object,default:function(){return{}}}},data:function(){return{loading:!1,STRATEGY_MAP:f["b"],fileList:[],scriptList:[],queryList:{deviceId:[],file:"",scriptName:"",scriptPath:"",execCycle:"",execDateType:"H",id:"",describe:"",execCommand:"",scriptId:null,value:null},formRules:{describe:[{required:!0,message:"请输入策略名称",trigger:"blur"},Object(T["b"])(),Object(T["c"])()],deviceId:[{type:"array",required:!0,message:"请选择设备类型",trigger:"change"}],scriptId:[{required:!0,message:"请选择采集脚本",trigger:"change"}],scriptName:[{required:!0,message:"请输入脚本名称",trigger:"blur"}],scriptPath:[{required:!0,message:"请输入脚本名称",trigger:"blur"}],execCycle:[{required:!0,message:"请输入执行周期",trigger:"blur"}],execCommand:[{required:!0,message:"请输入执行命令",trigger:"blur"},{max:1e3,message:"长度最多为1000个字符",trigger:["blur","change"]}]},converToFlow:_["a"]}},watch:{"queryList.deviceId":{handler:function(e){var t=this.$refs.cascaderRef&&this.$refs.cascaderRef.deviceList||[],i=this.getDeviceTypes(t,e);this.visible&&this.getScriptList(i)}},visible:{handler:function(e){if(e&&0!==Object.keys(this.value).length){var t=this.value,i=t.id,r=t.describe,s=t.execCycle,a=t.execDateType,n=t.deviceId,c=t.execCommand;this.queryList.id=i,this.queryList.describe=r,this.queryList.execCycle=s,this.queryList.execDateType=a,this.queryList.execCommand=c,this.queryList.deviceId=n}}}},computed:Object(x["a"])({},Object(L["b"])(["system"])),methods:{upload:function(e){var t=e.file;if(this.system.config.isLimitScriptFile){var i,r,s=t.name.split("."),a=Object(g["a"])(s,2),n=a[1],c=null===(i=this.system)||void 0===i?void 0:null===(r=i.config)||void 0===r?void 0:r.upload,u=c.type,l=c.size;if(!u.includes(n)||t.size>l)return this.handleRemove(),this.$message.error("只支持上传小于".concat(Object(_["a"])(l),"的.py, .sh格式脚本"))}this.fileList=[{name:t.name}],this.queryList.file=t},handleRemove:function(){this.fileList=[],this.queryList.file="",this.$refs.uploadRef.clearFiles()},close:function(){this.queryList={deviceId:[],file:"",scriptName:"",scriptPath:"",execCycle:"",execDateType:"H",id:"",describe:"",execCommand:""},this.scriptList=[],this.$refs.formRef.resetFields(),this.$emit("update:visible",!1)},confirm:function(){var e=this;this.$refs["formRef"].validate(function(){var t=Object(b["a"])(Object(h["a"])().mark((function t(i){var r,s,a,n,c;return Object(h["a"])().wrap((function(t){while(1)switch(t.prev=t.next){case 0:if(!i){t.next=26;break}if(e.loading=!0,r=Object(q["a"])({},e.queryList),s={},t.prev=4,!e.queryList.id){t.next=13;break}return delete r.file,delete r.scriptName,t.next=10,o(r);case 10:s=t.sent,t.next=17;break;case 13:return delete r.id,t.next=16,l(r);case 16:s=t.sent;case 17:a=1==s.data?s:s.data,n=a.code,c=a.message,200==n?e.$message.success("操作成功"):e.$message.error(c||"操作失败！"),e.$emit("refresh"),e.close(),t.next=25;break;case 23:t.prev=23,t.t0=t["catch"](4);case 25:e.loading=!1;case 26:case"end":return t.stop()}}),t,null,[[4,23]])})));return function(e){return t.apply(this,arguments)}}())},getScriptList:function(e){var t=this;return Object(b["a"])(Object(h["a"])().mark((function i(){var r;return Object(h["a"])().wrap((function(i){while(1)switch(i.prev=i.next){case 0:return i.next=2,p({systemType:e.join(",")});case 2:r=i.sent,200==r.code&&(t.scriptList=r.data);case 4:case"end":return i.stop()}}),i)})))()},scriptChange:function(){var e=this,t=this.scriptList.filter((function(t){return t.id==e.queryList.scriptId}))[0];t&&(this.queryList.scriptName=t.scriptName,this.queryList.execCommand=t.scriptCommand,this.queryList.scriptPath=t.scriptPath,this.queryList.value=void 0==t.value?null:t.value)},getDeviceTypes:function(e,t){var i=this,r=[];return e.forEach((function(e){if(e.children){var s=e.children.filter((function(e){return 1==t.includes(e.value)}));s&&s.length>0&&i.addDeviceTypeIfMatch(s[0],r)}})),r},addDeviceTypeIfMatch:function(e,t){e.root.indexOf("windows")>=0&&t.push("windows"),e.root.indexOf("linux")>=0&&t.push("linux")}}},C=w,k=i("2877"),$=Object(k["a"])(C,v,y,!1,null,null,null),D=$.exports,S={components:{FormDialog:D},data:function(){return{strategyListApi:c,STRATEGY_MAP:f["b"],SOURCE_MAP:f["a"],STATUS_MAP:m["a"],execDateType_Map:f["c"],queryList:{name:"",deviceType:""},dialog:{visible:!1,data:{}}}},computed:{tableFieldList:function(){var e,t,i;return(null===(e=this.$store.state.system)||void 0===e?void 0:null===(t=e.config)||void 0===t?void 0:null===(i=t.table)||void 0===i?void 0:i.strategy)||[]}},methods:{openFormDialog:function(e){0!==(null===e||void 0===e?void 0:e.source)&&2!=(null===e||void 0===e?void 0:e.strategyType)&&(this.dialog.data=e||{},this.dialog.visible=!0)},handleDelete:function(e){var t=this,i=e.id,r=e.source,s=e.strategyType;0!==r&&2!=s&&this.$confirm("您确定删除该条采集策略吗？","温馨提示",{type:"warning"}).then((function(){u({id:i}).then((function(){t.$message.success("删除成功"),t.search()}))}))},reset:function(){this.queryList.name="",this.queryList.deviceType="",this.search()},search:function(){this.$refs.tableRef.getDataList(this.queryList)},handleState:function(e){var t=this;0!==e.source&&2!=e.strategyType&&this.$confirm("您确定要".concat(1===e.state?"停用":"启用","吗？"),"温馨提示",{type:"warning"}).then((function(){t.loading=!0,d({id:e.id,state:0==e.state?1:0}).then((function(){t.$message.success("操作成功"),t.search()})).finally((function(){t.loading=!1}))}))}}},O=S,j=Object(k["a"])(O,r,s,!1,null,null,null);t["default"]=j.exports},"3dd8":function(e,t,i){"use strict";i.d(t,"b",(function(){return r})),i.d(t,"a",(function(){return s})),i.d(t,"c",(function(){return a}));i("4ec9"),i("d3b7"),i("3ca3"),i("ddb0");var r=new Map([[0,"流量采集"],[1,"终端采集"],[2,"协议采集"]]),s=new Map([[0,"系统自带"],[1,"手动添加"]]),a=new Map([["M","分钟"],["H","小时"]])},"7b54":function(e,t,i){"use strict";i.d(t,"a",(function(){return r}));i("4ec9"),i("d3b7"),i("3ca3"),i("ddb0");var r=new Map([[0,"未启用"],[1,"已启用"],[-1,"异常"]])},f656:function(e,t,i){"use strict";i.d(t,"b",(function(){return r})),i.d(t,"a",(function(){return s})),i.d(t,"c",(function(){return a}));i("ac1f"),i("00b4"),i("d9e2");function r(){return{min:1,max:50,message:"长度在1-50个字符",trigger:["blur","change"]}}function s(){return{max:200,message:"长度最多为200个字符",trigger:["blur","change"]}}function a(){return{validator:function(e,t,i){var r=/^[a-zA-Z0-9!@#$%^&*()_=|\\（）+\/\u4e00-\u9fa5-]+$/;r.test(t)?i():i(new Error("只能包含中文、字母、数字和!@#$%^&*()_=|\\（）-+/"))},trigger:["blur","change"]}}}}]);