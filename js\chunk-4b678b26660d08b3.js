(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-4b678b26"],{"0c29":function(e,t,a){"use strict";var r=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("section",{class:e.prefixCls},[a("p",{class:[e.prefixCls+"-title",1==e.border?"has-border":null]},[a("span",{class:e.prefixCls+"-title__text"},[e._v(e._s(e.title))]),e._v(" "),e._t("right")],2),e._v(" "),a("section",[e._t("default")],2)])},s=[],i=a("ef44"),n={name:"CommonSection",props:{title:{type:String,default:""},border:{type:Boolean,default:!1}},computed:{prefixCls:function(){var e=Object(i["e"])("section"),t=e.prefixCls;return t}}},o=n,c=(a("52b1"),a("2877")),l=Object(c["a"])(o,r,s,!1,null,"51fc79ae",null);t["a"]=l.exports},"440cc":function(e,t,a){},"52b1":function(e,t,a){"use strict";var r=a("440cc"),s=a.n(r);s.a},7021:function(e,t,a){},ac00:function(e,t,a){"use strict";a.r(t);var r=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("div",{directives:[{name:"loading",rawName:"v-loading",value:e.loading,expression:"loading"}],class:e.prefixCls},[a("div",{class:e.prefixCls+"__left"},[e._e(),e._v(" "),a("div",{class:e.prefixCls+"__left--list"},[a("div",{class:e.prefixCls+"__left--list--header"},[a("uxd-button",{attrs:{"is-icon":"",icon:"uxd-icon-add-circle"},on:{click:e.handleAdd}}),e._v(" "),a("uxd-button",{attrs:{"is-icon":"",icon:"uxd-icon-delete"},on:{click:e.handleDelete}}),e._v(" "),a("uxd-button",{attrs:{"is-icon":"",icon:"uxd-icon-refresh"},on:{click:e.search}})],1),e._v(" "),a("div",{class:e.prefixCls+"__left--list--content"},[a("uxd-scrollbar",[a("uxd-tree",{ref:"treeRef",attrs:{data:e.list,props:e.defaultProps,"node-key":"id","highlight-current":""},on:{"node-click":e.handleNodeClick}})],1),e._v(" "),a("common-add-edit-dialog",{ref:"commomDialog",attrs:{visible:e.dialog.addEditDialog.visible,title:e.dialog.addEditDialog.title,data:e.dialog.addEditDialog.data,config:e.dialog.addEditDialog.config,labelWidth:"190px",width:"600px"},on:{"update:visible":function(t){return e.$set(e.dialog.addEditDialog,"visible",t)},success:e.search},scopedSlots:e._u([{key:"custom",fn:function(t){var r=t.propItem,s=t.dialogData;return["minResource"==r.prop||"maxResource"==r.prop||"reservedResource"==r.prop?[a("div",[a("div",{staticStyle:{display:"flex","justify-content":"space-between","margin-bottom":"24px"}},[a("uxd-input-number",{attrs:{min:0},model:{value:s[r.prop].resourcePercent,callback:function(t){e.$set(s[r.prop],"resourcePercent",t)},expression:"\n                                            dialogData[propItem.prop]\n                                                .resourcePercent\n                                        "}}),e._v(" "),a("span",{staticStyle:{"flex-shrink":"0",margin:"0px 5px",width:"50px"}},[e._v("\n                                        %\n                                    ")])],1),e._v(" "),a("div",{staticStyle:{display:"flex","justify-content":"space-between","margin-bottom":"24px"}},[a("uxd-input-number",{attrs:{min:0},model:{value:s[r.prop].resourceValue,callback:function(t){e.$set(s[r.prop],"resourceValue",t)},expression:"\n                                            dialogData[propItem.prop]\n                                                .resourceValue\n                                        "}}),e._v(" "),a("span",{staticStyle:{"flex-shrink":"0",margin:"0px 5px",width:"50px"}},[e._v("\n                                        MB\n                                    ")])],1),e._v(" "),a("div",{staticStyle:{display:"flex","justify-content":"space-between"}},[a("uxd-input-number",{attrs:{min:0},model:{value:s[r.prop].cpuVCores,callback:function(t){e.$set(s[r.prop],"cpuVCores",t)},expression:"\n                                            dialogData[propItem.prop]\n                                                .cpuVCores\n                                        "}}),e._v(" "),a("span",{staticStyle:{"flex-shrink":"0",margin:"0px 5px",width:"50px"}},[e._v("\n                                        vCores\n                                    ")])],1)])]:e._e()]}}])})],1)])]),e._v(" "),a("div",{class:e.prefixCls+"__right"},[a("common-section",{attrs:{title:"概述",border:!0}},[e.detailData?a("uxd-scrollbar",{staticClass:"summary-info"},[a("div",{staticClass:"info description"},[a("p",[e._v("描述：")]),e._v(" "),a("uxd-tooltip",{attrs:{effect:"dark",content:e.detailData.description,placement:"right-start"}},[a("p",{staticClass:"value"},[e._v(e._s(e.detailData.description))])])],1),e._v(" "),a("div",{staticClass:"info"},[a("p",{staticClass:"info--header"},[e._v("基础信息")]),e._v(" "),a("div",{staticClass:"info--content"},[a("div",{staticClass:"info--items"},[a("div",{staticClass:"item"},[a("p",[e._v("名称：")]),e._v(" "),a("p",[e._v(e._s(e.detailData.name))])]),e._v(" "),a("div",{staticClass:"item"},[a("p",[e._v("租户类型：")]),e._v(" "),a("p",[e._v("\n                                    "+e._s(e.dialog.addEditDialog.config.props.find((function(e){return"leafTenant"==e.prop})).map.find((function(t){return t.value==e.detailData.leafTenant})).label)+"\n                                ")])]),e._v(" "),a("div",{staticClass:"item"},[a("p",[e._v("租户状态：")]),e._v(" "),a("p",{class:["status","正常"==e.detailData.status?"normal":""]},[e._v("\n                                    "+e._s(e.detailData.status)+"\n                                ")])])])])]),e._v(" "),a("div",{staticClass:"info"},[a("p",{staticClass:"info--header"},[e._v("Yarn")]),e._v(" "),a("div",{staticClass:"info--content"},[a("p",{staticClass:"item"},[e._v("资源容量：")]),e._v(" "),a("div",{staticClass:"info--items"},e._l(["min","max","reserved"],(function(t){return a("p",{key:t,staticClass:"item"},[e._v("\n                                "+e._s(t)+"：memory\n                                "+e._s(e.getResource(t).resourceValue)+"，vcores\n                                "+e._s(e.getResource(t).cpuVCores)+"："+e._s(e.getResource(t).resourcePercent)+"%\n                            ")])})),0),e._v(" "),a("p",[e._v("\n                            权重："),e.detailData.resourceAllocation?a("span",[e._v(e._s(e.detailData.resourceAllocation.resourceWeight))]):e._e()])])]),e._v(" "),e.detailData.spaceQuota?a("div",{staticClass:"info"},[a("p",{staticClass:"info--header"},[e._v("HDFS")]),e._v(" "),a("div",{staticClass:"info--content"},[a("div",{staticClass:"item"},[a("p",[e._v("存储空间配额：")]),e._v(" "),a("p",[e._v("\n                                "+e._s(e.formatBytes(1*e.detailData.spaceQuota*1024*1024))+"\n                            ")])])])]):e._e(),e._v(" "),a("div",{staticClass:"info resource"},[a("p",{staticClass:"info--header"},[e._v("资源配额")]),e._v(" "),a("edr-table",{ref:"tableRef",attrs:{api:e.getResourceList,tableFieldList:e.tableFieldList,header:!1,hasPagination:!1}})],1),e._v(" "),a("div",{staticClass:"info"},[a("p",{staticClass:"info--header"},[e._v("图表")]),e._v(" "),a("p",{staticClass:"info--charts-condition"},[a("uxd-radio-group",{on:{change:e.getCharts},model:{value:e.curTime,callback:function(t){e.curTime=t},expression:"curTime"}},e._l(e.timeList,(function(t){return a("uxd-radio-button",{key:t.label,attrs:{label:t.value,plain:""}},[e._v("\n                                "+e._s(t.label)+"\n                            ")])})),1)],1),e._v(" "),a("div",{staticClass:"info--charts"},[a("div",{staticClass:"info--chart calc-resource"},[a("p",{staticClass:"info--chart--header"},[e._v("计算资源")]),e._v(" "),a("div",{staticClass:"info--chart--content"},e._l([e.detailChart.rm_allocatedvcores_group.chartData,e.detailChart.rm_allocatedmb_group.chartData],(function(e){return a("div",{key:e.title.text,staticStyle:{height:"100%",width:"100%"}},[a("echarts-line",{attrs:{title:e.title,grid:e.grid,legend:e.legend,xAxis:e.xAxis,series:e.series}})],1)})),0)]),e._v(" "),a("div",{staticClass:"info--chart store-resource"},[a("p",{staticClass:"info--chart--header"},[e._v("存储资源")]),e._v(" "),a("div",{staticClass:"info--chart--content"},e._l([e.detailChart.nn_fileanddir_group.chartData,e.detailChart.nn_space_group.chartData],(function(e){return a("div",{key:e.title.text,staticStyle:{height:"100%",width:"100%"}},[a("echarts-line",{attrs:{title:e.title,grid:e.grid,legend:e.legend,xAxis:e.xAxis,series:e.series}})],1)})),0)])])])]):e._e()],1)],1)])},s=[],i=a("5530"),n=a("c7eb"),o=a("1da1"),c=(a("ac1f"),a("841c"),a("d3b7"),a("b0c0"),a("7db0"),a("b64b"),a("d81d"),a("159b"),a("14d9"),a("d232")),l=a("4f8d"),u=a("2062"),d=!1;function p(e){return d?new Promise((function(e){for(var t=[{id:-1,name:"default"}],a=1;a<=100;a++)t.push({id:a,name:"租户".concat(a)});setTimeout((function(){e({code:200,data:t})}),500)})):c["a"].get({url:l["c"].tenancyResourceList,params:e},{baseUrl:u["b"]})}function f(e){return d?new Promise((function(e){setTimeout((function(){e({code:200,data:{id:"xxx",name:"hwtest",status:"正常",leafTenant:!0,description:"描述呀呀呀1111111111111描述呀呀呀1111111111111描述呀呀呀1111111111111描述呀呀呀1111111111111描述呀呀呀1111111111111描述呀呀呀1111111111111描述呀呀呀1111111111111描述呀呀呀1111111111111描述呀呀呀1111111111111描述呀呀呀1111111111111描述呀呀呀1111111111111描述呀呀呀1111111111111描述呀呀呀1111111111111描述呀呀呀1111111111111描述呀呀呀1111111111111描述呀呀呀1111111111111描述呀呀呀1111111111111描述呀呀呀1111111111111描述呀呀呀1111111111111描述呀呀呀1111111111111描述呀呀呀1111111111111描述呀呀呀1111111111111描述呀呀呀1111111111111描述呀呀呀1111111111111描述呀呀呀1111111111111描述呀呀呀1111111111111描述呀呀呀1111111111111描述呀呀呀1111111111111描述呀呀呀1111111111111描述呀呀呀1111111111111描述呀呀呀1111111111111描述呀呀呀1111111111111描述呀呀呀1111111111111描述呀呀呀1111111111111描述呀呀呀1111111111111描述呀呀呀1111111111111描述呀呀呀1111111111111描述呀呀呀1111111111111描述呀呀呀1111111111111描述呀呀呀1111111111111描述呀呀呀1111111111111描述呀呀呀1111111111111描述呀呀呀1111111111111描述呀呀呀1111111111111描述呀呀呀1111111111111描述呀呀呀1111111111111描述呀呀呀1111111111111描述呀呀呀1111111111111描述呀呀呀1111111111111描述呀呀呀1111111111111描述呀呀呀1111111111111描述呀呀呀1111111111111描述呀呀呀1111111111111描述呀呀呀1111111111111描述呀呀呀1111111111111描述呀呀呀1111111111111描述呀呀呀1111111111111描述呀呀呀1111111111111描述呀呀呀1111111111111描述呀呀呀1111111111111描述呀呀呀1111111111111描述呀呀呀11111111111111111111122222222222222222222222222222222描述呀呀呀11111111111111111111122222222222222222222222222222222描述呀呀呀11111111111111111111122222222222222222222222222222222描述呀呀呀11111111111111111111122222222222222222222222222222222描述呀呀呀11111111111111111111122222222222222222222222222222222222333333333333333333333222333333333333333333333222333333333333333333333222333333333333333333333222333333333333333333333",resourceModel:{calcResource:{serviceName:"Yarn",superSchedulerProperties:{resourceAllocations:[{resourcePoolName:"default",resourceAllocation:{resourceWeight:20,minResourcePercent:20,minResourceValue:0,minCpuVCores:0,maxResourcePercent:20,maxResourceValue:0,maxCpuVCores:0,reservedResourcePercent:20,reservedResourceValue:0,reservedCpuVCores:0}}]}},storageResource:[{serviceName:"HDFS",catalogResources:[{spaceQuota:1024e3}]}],resourceQuotaList:[{serviceName:"HDFS",resourceName:"space",percent:"0.00%",available:"102400MB",usage:"0MB"},{serviceName:"HDFS",resourceName:"space",percent:"0.00%",available:"102400MB",usage:"0MB"},{serviceName:"HDFS",resourceName:"space",percent:"0.00%",available:"102400MB",usage:"0MB"}]}}})}),500)})):c["a"].post({url:l["c"].tenancyResourceDetail,params:e},{baseUrl:u["b"]})}function v(e){return console.log(e,"tenancyResourceChartApi"),d?new Promise((function(t){setTimeout((function(){t({code:200,data:"default"==e.tenant?[]:[{name:"分配的最大核数",datas:[{xvalue:"2020-04-05 00:00:00",yvalue:142},{xvalue:"2020-04-05 00:10:00",yvalue:122},{xvalue:"2020-04-05 00:20:00",yvalue:112}]},{name:"使用的大核数",datas:[{xvalue:"2020-04-05 00:00:00",yvalue:13},{xvalue:"2020-04-05 00:10:00",yvalue:20},{xvalue:"2020-04-05 00:20:00",yvalue:25}]}]})}),500)})):c["a"].post({url:l["c"].tenancyResourceChart,params:e},{baseUrl:u["b"]})}function m(e){return d?new Promise((function(e){setTimeout((function(){e({code:200})}),1e4)})):c["a"].post({url:l["c"].addTenancyResource,params:e},{baseUrl:u["b"]})}function g(e){return c["a"].post({url:l["c"].editTenancyResource,params:e},{baseUrl:u["b"]})}function h(e){return c["a"].post({url:l["c"].deleteTenancyResource,params:e},{baseUrl:u["b"]})}var b=a("f656"),x=a("2ef0"),_=a.n(x),C=a("0c29"),y=a("5fda"),R=a("ef44"),D={components:{CommonSection:C["a"],echartsLine:y["b"]},data:function(){return{formatBytes:R["b"],prefixCls:"tenancy-resource-page",loading:!1,dialog:{addEditDialog:{visible:!1,title:"添加租户",data:{},config:{props:[{prop:"name",label:"名称",rules:[{required:!0,message:"请输入名称",trigger:"blur"},Object(b["b"])()]},{prop:"leafTenant",label:"租户类型",componentType:"radio",map:[{label:"叶子租户",value:!0},{label:"非叶子租户",value:!1}],rules:[{required:!0,message:"请选择租户类型",trigger:"blur"}]},{prop:"configMode",label:"配置模式",componentType:"radio",map:[{label:"基础",value:0},{label:"高级",value:1}],rules:[{required:!0,message:"请选择配置模式",trigger:"blur"}]},{prop:"defaultResourcePercent",label:"默认资源池容量(%)",type:"Number",min:0,max:100,showHandle:function(e){return 0==e.configMode},rules:[{required:!0,message:"请输入默认资源池容量",trigger:"blur"}]},{prop:"resourceWeight",label:"权重",type:"Number",min:0,max:100,showHandle:function(e){return 1==e.configMode},rules:[{required:!0,message:"请输入权重",trigger:"blur"}]},{prop:"minResource",label:"最小资源",type:"custom",showHandle:function(e){return 1==e.configMode},rules:[{required:!0,validator:function(e,t,a){a()},trigger:"blur"}]},{prop:"maxResource",label:"最大资源",type:"custom",showHandle:function(e){return 1==e.configMode},rules:[{required:!0,validator:function(e,t,a){a()},trigger:"blur"}]},{prop:"reservedResource",label:"预留资源",type:"custom",showHandle:function(e){return 1==e.configMode},rules:[{required:!0,validator:function(e,t,a){a()},trigger:"blur"}]},{prop:"store",label:"存储空间配额（MB）",type:"Number",min:0,rules:[{required:!0,message:"请输入存储空间配额",trigger:"blur"}]},{prop:"fileNumLimit",label:"文件/目录上限",type:"Number",min:0,rules:[{required:!0,message:"请输入文件/目录上限",trigger:"blur"}]},{prop:"description",label:"备注",rules:[{max:200,message:"长度最多为200个字符",trigger:["blur","change"]},Object(b["a"])()]}],addApi:this.handleAddApi,editApi:g}}},defaultProps:{children:"children",label:"name"},list:[],current:null,currentKey:"",activeName:"概述",tableFieldList:[{prop:"serviceName",label:"服务"},{prop:"resourceName",label:"资源"},{prop:"percent",label:"百分比"},{prop:"available",label:"可用"},{prop:"usage",label:"已使用"}],addDefaultData:{name:"",leafTenant:!0,hasChildren:!1,parentId:"",description:"",resourceModel:{calcResource:{capacitySchedulerProperties:{},resourceType:"CALC_RESOURCE",relationType:"SHARE",configMode:"SUPER",serviceName:"Yarn",superSchedulerProperties:{resourceAllocations:[{resourcePoolName:"default",resourceAllocation:{resourceWeight:20,minResourcePercent:20,minResourceValue:0,minCpuVCores:0,maxResourcePercent:20,maxResourceValue:0,maxCpuVCores:0,reservedResourcePercent:20,reservedResourceValue:0,reservedCpuVCores:0}}]}},storageResource:[{relationType:"SHARE",resourceType:"STORE_RESOURCE",serviceName:"HDFS",catalogResources:[{storagePath:"/tenant/",fileNumLimit:1e4,spaceQuota:1024e3}]}]}},detailData:null,detailChart:{rm_allocatedvcores_group:{params:{serviceName:"Yarn",type:"rm_allocatedvcores_group"},chartData:{empty:!0,xAxis:{data:[]},series:[{data:[]}],title:{text:"Yarn当前已分配的CPU核数",subtext:"单位：vCores",top:16,left:16,textStyle:{color:"#fff",fontSize:14,fontWeight:"bold"},subtextStyle:{color:"#fff",fontSize:12}},grid:{top:70,bottom:40,left:46,right:20},legend:{show:!1}}},rm_allocatedmb_group:{params:{serviceName:"Yarn",type:"rm_allocatedmb_group"},chartData:{empty:!0,xAxis:{data:[]},series:[{data:[]}],title:{text:"Yarn当前已分配的内存",subtext:"",top:16,left:16,textStyle:{color:"#fff",fontSize:14,fontWeight:"bold"},subtextStyle:{color:"#fff",fontSize:12}},grid:{top:70,bottom:40,left:66,right:20},legend:{show:!1}}},nn_fileanddir_group:{params:{serviceName:"HDFS",type:"nn_fileanddir_group"},chartData:{empty:!0,xAxis:{data:[]},series:[{data:[]}],title:{text:"HDFS租户文件对象信息",subtext:"",top:16,left:16,textStyle:{color:"#fff",fontSize:14,fontWeight:"bold"},subtextStyle:{color:"#fff",fontSize:12}},grid:{top:70,bottom:40,left:46,right:20},legend:{show:!1}}},nn_space_group:{params:{serviceName:"HDFS",type:"nn_space_group"},chartData:{empty:!0,xAxis:{data:[]},series:[{data:[]}],title:{text:"HDFS租户空间信息",subtext:"",top:16,left:16,textStyle:{color:"#fff",fontSize:14,fontWeight:"bold"},subtextStyle:{color:"#fff",fontSize:12}},grid:{top:70,bottom:40,left:66,right:20},legend:{show:!1}}}},timeList:[{label:"实时",value:"1H"},{label:"4小时",value:"4H"},{label:"8小时",value:"8H"},{label:"12小时",value:"12H"},{label:"1天",value:"1D"},{label:"1周",value:"1W"},{label:"1月",value:"1M"}],curTime:"1H"}},mounted:function(){this.search()},methods:{search:function(){var e=this;return Object(o["a"])(Object(n["a"])().mark((function t(){var a;return Object(n["a"])().wrap((function(t){while(1)switch(t.prev=t.next){case 0:return e.loading=!0,t.prev=1,t.next=4,p();case 4:a=t.sent,e.list=a.data,e.list.length>0&&(e.handleNodeClick(e.list[0]),e.$nextTick((function(){e.$refs.treeRef.setCurrentKey(e.current.id)})));case 7:return t.prev=7,e.loading=!1,t.finish(7);case 10:case"end":return t.stop()}}),t,null,[[1,,7,10]])})))()},handleAdd:function(){var e={configMode:0,defaultResourcePercent:20,resourceWeight:20,store:1024e3,fileNumLimit:1e4},t=["minResource","maxResource","reservedResource"];t.forEach((function(t){e[t]={},e[t].resourcePercent=20,e[t].resourceValue=0,e[t].cpuVCores=0})),e["minResource"].resourcePercent=20,e["maxResource"].resourcePercent=100,e["reservedResource"].resourcePercent=10,this.dialog.addEditDialog.data=_.a.merge(e,_.a.cloneDeep(this.addDefaultData)),this.dialog.addEditDialog.visible=!0},handleAddApi:function(e){var t,a,r,s=_.a.cloneDeep(e),i=s.resourceModel.storageResource[0].catalogResources[0],n=null===s||void 0===s?void 0:null===(t=s.resourceModel)||void 0===t?void 0:null===(a=t.calcResource)||void 0===a?void 0:null===(r=a.superSchedulerProperties)||void 0===r?void 0:r.resourceAllocations[0].resourceAllocation;if(n)if(0==s.configMode)n.minResourcePercent=s.defaultResourcePercent;else{n.resourceWeight=s.resourceWeight;var o=["min","max","reserved"];o.forEach((function(e){n["".concat(e,"ResourcePercent")]=s["".concat(e,"Resource")].resourcePercent,n["".concat(e,"ResourceValue")]=s["".concat(e,"Resource")].resourceValue,n["".concat(e,"CpuVCores")]=s["".concat(e,"Resource")].cpuVCores,delete s["".concat(e,"Resource")]}))}return i.fileNumLimit=s.fileNumLimit,i.spaceQuota=s.store,i.storagePath="/tenant/".concat(s.name),delete s.configMode,delete s.defaultResourcePercent,delete s.resourceWeight,delete s.store,delete s.fileNumLimit,m(s)},handleEdit:function(){this.dialog.addEditDialog.visible=!0},handleDelete:function(){var e=this;"default"!=this.current.name?this.$confirm("确认删除该租户?","提示",{confirmButtonText:"确认",cancelButtonText:"取消",type:"warning"}).then(Object(o["a"])(Object(n["a"])().mark((function t(){var a;return Object(n["a"])().wrap((function(t){while(1)switch(t.prev=t.next){case 0:return t.next=2,h({id:e.current.id});case 2:a=t.sent,200==a.code&&(e.detailData=null,e.search(),e.$message({message:"删除成功！",type:"success"}));case 4:case"end":return t.stop()}}),t)})))).catch((function(){})):this.$message({message:"默认租户不允许删除！",type:"warning"})},handleNodeClick:function(e){this.current=e,this.getDetail(),this.getCharts()},getDetail:function(){var e=this;return Object(o["a"])(Object(n["a"])().mark((function t(){var a,r,s,o,c,l,u,d,p;return Object(n["a"])().wrap((function(t){while(1)switch(t.prev=t.next){case 0:return t.next=2,f({id:e.current.id});case 2:a=t.sent,200==a.code&&(u=null===(r=a.data.resourceModel)||void 0===r?void 0:null===(s=r.calcResource)||void 0===s?void 0:null===(o=s.superSchedulerProperties)||void 0===o?void 0:o.resourceAllocations[0].resourceAllocation,d=null===(c=a.data.resourceModel)||void 0===c?void 0:null===(l=c.storageResource)||void 0===l?void 0:l.find((function(e){return"HDFS"==e.serviceName})),p=null,d&&d.catalogResources.length>0&&(p=d.catalogResources[0].spaceQuota),e.detailData=Object(i["a"])(Object(i["a"])({},a.data),{},{resourceAllocation:u,spaceQuota:p}));case 4:case"end":return t.stop()}}),t)})))()},getCharts:function(){var e=this;return Object(o["a"])(Object(n["a"])().mark((function t(){var a,r,s;return Object(n["a"])().wrap((function(t){while(1)switch(t.prev=t.next){case 0:a=0,r=Object.keys(e.detailChart);case 1:if(!(a<r.length)){t.next=8;break}return s=r[a],t.next=5,e.getChart(e.detailChart[s]);case 5:a++,t.next=1;break;case 8:case"end":return t.stop()}}),t)})))()},getChart:function(e){var t=this;return Object(o["a"])(Object(n["a"])().mark((function a(){var r;return Object(n["a"])().wrap((function(a){while(1)switch(a.prev=a.next){case 0:return a.prev=0,e.chartData.xAxis.data=[],e.chartData.series=[],a.next=5,v(Object(i["a"])({tenant:t.current.name,time:t.curTime},e.params));case 5:r=a.sent,200==r.code&&(e.chartData.empty=0==r.data.length,r.data.length>0&&(e.chartData.xAxis.data=r.data[0].datas.map((function(e){return e.xvalue})),e.chartData.series=[],r.data.forEach((function(t){e.chartData.series.push({name:t.name,data:t.datas.map((function(e){return e.yvalue}))})})))),a.next=11;break;case 9:a.prev=9,a.t0=a["catch"](0);case 11:case"end":return a.stop()}}),a,null,[[0,9]])})))()},getResource:function(e){var t={resourcePercent:0,resourceValue:0,cpuVCores:0},a=this.detailData.resourceAllocation;return a&&(t.resourcePercent=a["".concat(e,"ResourcePercent")],t.resourceValue=a["".concat(e,"ResourceValue")],t.cpuVCores=a["".concat(e,"CpuVCores")]),t},getResourceList:function(){var e=this;return Object(o["a"])(Object(n["a"])().mark((function t(){var a,r;return Object(n["a"])().wrap((function(t){while(1)switch(t.prev=t.next){case 0:return t.abrupt("return",{code:200,data:(null===(a=e.detailData)||void 0===a?void 0:null===(r=a.resourceModel)||void 0===r?void 0:r.resourceQuotaList)||[]});case 1:case"end":return t.stop()}}),t)})))()}}},w=D,S=(a("c88e"),a("2877")),P=Object(S["a"])(w,r,s,!1,null,"a2649d5c",null);t["default"]=P.exports},c88e:function(e,t,a){"use strict";var r=a("7021"),s=a.n(r);s.a},f656:function(e,t,a){"use strict";a.d(t,"b",(function(){return r})),a.d(t,"a",(function(){return s})),a.d(t,"c",(function(){return i}));a("ac1f"),a("00b4"),a("d9e2");function r(){return{min:1,max:50,message:"长度在1-50个字符",trigger:["blur","change"]}}function s(){return{max:200,message:"长度最多为200个字符",trigger:["blur","change"]}}function i(){return{validator:function(e,t,a){var r=/^[a-zA-Z0-9!@#$%^&*()_=|\\（）+\/\u4e00-\u9fa5-]+$/;r.test(t)?a():a(new Error("只能包含中文、字母、数字和!@#$%^&*()_=|\\（）-+/"))},trigger:["blur","change"]}}}}]);