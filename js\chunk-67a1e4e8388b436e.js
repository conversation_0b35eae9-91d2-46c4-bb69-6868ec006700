(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-67a1e4e8"],{"0c29":function(e,t,n){"use strict";var a=function(){var e=this,t=e.$createElement,n=e._self._c||t;return n("section",{class:e.prefixCls},[n("p",{class:[e.prefixCls+"-title",1==e.border?"has-border":null]},[n("span",{class:e.prefixCls+"-title__text"},[e._v(e._s(e.title))]),e._v(" "),e._t("right")],2),e._v(" "),n("section",[e._t("default")],2)])},r=[],s=n("ef44"),o={name:"CommonSection",props:{title:{type:String,default:""},border:{type:Boolean,default:!1}},computed:{prefixCls:function(){var e=Object(s["e"])("section"),t=e.prefixCls;return t}}},i=o,c=(n("52b1"),n("2877")),l=Object(c["a"])(i,a,r,!1,null,"51fc79ae",null);t["a"]=l.exports},2805:function(e,t,n){},4089:function(e,t,n){"use strict";var a=n("2805"),r=n.n(a);r.a},"440cc":function(e,t,n){},"52b1":function(e,t,n){"use strict";var a=n("440cc"),r=n.n(a);r.a},6458:function(e,t,n){"use strict";n.d(t,"a",(function(){return a})),n.d(t,"b",(function(){return r}));n("4ec9"),n("d3b7"),n("3ca3"),n("ddb0"),new Map([[0,{label:"停止",theme:"info"}],[1,{label:"告警",theme:"danger"}],[2,{label:"正常",theme:"success"},,]]);var a=new Map([["UNKNOWN",{label:"未知",theme:"info"}],["CONFIGURING",{label:"已配置",theme:"success"}],["FAILED",{label:"失败",theme:"danger"}],["EXPIRED",{label:"过期",theme:"warning"}],["SYNCHRONIZED",{label:"已同步",theme:"success"}]]),r=new Map([["1",{label:"正常",theme:"success"}],["2",{label:"掉线",theme:"info"}],["3",{label:"异常",theme:"danger"}]])},fa06:function(e,t,n){"use strict";var a=function(){var e=this,t=e.$createElement,n=e._self._c||t;return n("uxd-split",{staticClass:"operation-page"},[n("uxd-split-pane",{staticClass:"operation-page__componet",attrs:{size:"20"}},[n("common-section",{attrs:{title:"服务列表",border:!0}},[n("template",{slot:"right"},[n("span",{staticClass:"confirm-btn",attrs:{size:"small"},on:{click:e.handleAdd}},[e._v("\n                    添加服务\n                ")]),e._v(" "),n("span",{staticClass:"cancel-btn",on:{click:e.search}},[e._v("刷新")])]),e._v(" "),n("edr-table",{ref:"tableRef",attrs:{tableFieldList:e.tableFieldListComponent,api:e.getComponentList,params:e.queryListForComponent,hasPagination:!1,header:!1},on:{"row-click":e.handleSelect}},[n("uxd-table-column",{attrs:{width:"200px",label:"操作"},scopedSlots:e._u([{key:"default",fn:function(t){var a=t.row;return[n("uxd-button",{attrs:{type:"text",primary:"",disabled:2==a.serviceStateCode},on:{click:function(t){return e.handleRunStop(a)}}},[e._v("\n                            启动\n                        ")]),e._v(" "),n("uxd-button",{attrs:{type:"text",primary:""},on:{click:function(t){return e.handleRestart(a)}}},[e._v("\n                            重启\n                        ")]),e._v(" "),n("uxd-button",{attrs:{type:"text",primary:"",disabled:4==a.serviceStateCode},on:{click:function(t){return e.handleRunStop(a,"停止")}}},[e._v("\n                            停止\n                        ")]),e._v(" "),n("uxd-button",{attrs:{type:"text",primary:""},on:{click:function(t){return e.handleDelete(a)}}},[e._v("\n                            删除\n                        ")])]}}])})],1)],2),e._v(" "),n("common-add-edit-dialog",{attrs:{customClass:"add-service-dialog",visible:e.dialog.addService.visible,title:e.dialog.addService.title,data:e.dialog.addService.data,config:e.dialog.addService.config,width:"600px",height:"300px"},on:{"update:visible":function(t){return e.$set(e.dialog.addService,"visible",t)},success:e.search,cancel:e.handleCancel},scopedSlots:e._u([{key:"custom",fn:function(t){var a=t.propItem;return["progress"==a.prop?[null!=e.dialog.addService.data.progress?n("uxd-progress",{attrs:{percentage:e.dialog.addService.data.progress}}):e._e()]:e._e()]}}])})],1),e._v(" "),n("uxd-split-pane",{staticClass:"operation-page__componet-host",attrs:{size:"80"}},[n("common-section",{attrs:{title:"组件实例",border:!0}},[n("edr-table",{ref:"tableComponentHostRef",attrs:{tableFieldList:e.tableFieldListhostComponent,api:e.getComponentHostList,params:e.queryListForComponentHost,isMountedGetData:!1,hasPagination:!1,header:!1},scopedSlots:e._u([{key:"runningStatus",fn:function(t){var a=t.row;return[e.COMPONENT_SERVICE_RUNNINGSTATUS_MAP.get(a.runningStatus)?n("uxd-tag",{staticClass:"tag--status",attrs:{type:e.COMPONENT_SERVICE_RUNNINGSTATUS_MAP.get(a.runningStatus).theme}},[e._v("\n                        "+e._s(e.COMPONENT_SERVICE_RUNNINGSTATUS_MAP.get(a.runningStatus).label)+"\n                    ")]):e._e()]}},{key:"configStatus",fn:function(t){var a=t.row;return[e.COMPONENT_SERVICE_CONFIGSTATUS_MAP.get(a.configStatus)?n("uxd-tag",{staticClass:"tag--status",attrs:{type:e.COMPONENT_SERVICE_CONFIGSTATUS_MAP.get(a.configStatus).theme}},[e._v("\n                        "+e._s(e.COMPONENT_SERVICE_CONFIGSTATUS_MAP.get(a.configStatus).label)+"\n                    ")]):e._e()]}}])})],1)],1)],1)},r=[],s=n("5530"),o=n("c7eb"),i=n("1da1"),c=(n("4de4"),n("d3b7"),n("a9e3"),n("d81d"),n("ac1f"),n("841c"),n("14d9"),n("0c29")),l=n("f099"),u=n("6458"),d={components:{CommonSection:c["a"]},computed:{tableFieldListComponent:function(){var e,t,n,a=(null===(e=this.$store.state.system)||void 0===e?void 0:null===(t=e.config)||void 0===t?void 0:null===(n=t.table)||void 0===n?void 0:n.operation)||[];return a},tableFieldListhostComponent:function(){var e,t,n,a=(null===(e=this.$store.state.system)||void 0===e?void 0:null===(t=e.config)||void 0===t?void 0:null===(n=t.table)||void 0===n?void 0:n.hostComponent.filter((function(e){return!1!==e.show})))||[];return a}},props:{type:{type:Number,default:0}},data:function(){var e=this;return{COMPONENT_SERVICE_CONFIGSTATUS_MAP:u["a"],COMPONENT_SERVICE_RUNNINGSTATUS_MAP:u["b"],getComponentListApi:l["c"],getComponentHostListApi:l["b"],getInstallServiceApi:l["d"],getInstallServiceProgressApi:l["e"],queryListForComponent:{},queryListForComponentHost:{no_page:!1},componentHostList:[],dialog:{addService:{visible:!1,title:"添加服务",data:{},config:{props:[{label:"安装服务",prop:"serviceId",mapRemoteApi:l["d"],mapRemoteQuery:function(t,n){return{name:t,type:e.type}},mapRemoteResultHandle:function(e){return e.map((function(e){return{label:e.serviceName,value:e.id}}))},map:[],rules:[{required:!0,message:"请选择需要安装的服务!",trigger:"blur"}]},{label:"",prop:"progress",type:"custom"}],addApi:this.handleInstall}},serviceProgress:{}},installFieldList:[{prop:"serviceName",label:"服务"},{prop:"serviceDesc",label:"描述"},{prop:"serviceVersion",label:"版本"},{prop:"installed",label:"是否安装"}],installProgressFieldList:[{prop:"commandName",label:"命令"},{prop:"commandState",label:"状态"},{prop:"createTime",label:"开始时间"},{prop:"durationTime",label:"持续时间"}]}},methods:{handleRunStop:function(e){var t=this,n=arguments.length>1&&void 0!==arguments[1]?arguments[1]:"启动";this.$msgbox({message:"确定".concat(n,"此服务？"),confirmButtonText:n,cancelButtonText:"取消",showCancelButton:!0,showConfirmButton:!0}).then(Object(i["a"])(Object(o["a"])().mark((function a(){var r,s;return Object(o["a"])().wrap((function(a){while(1)switch(a.prev=a.next){case 0:return r="启动"==n?l["h"]:l["i"],a.next=3,r({serviceId:e.id});case 3:s=a.sent,200==s.code&&(t.$message.success("".concat(n,"成功")),t.search());case 5:case"end":return a.stop()}}),a)}))))},handleDelete:function(e){var t=this;this.$msgbox({message:"确定删除此服务？",confirmButtonText:"删除",cancelButtonText:"取消",showCancelButton:!0,showConfirmButton:!0,confirmButtonClass:"uxd-button--danger"}).then(Object(i["a"])(Object(o["a"])().mark((function n(){var a;return Object(o["a"])().wrap((function(n){while(1)switch(n.prev=n.next){case 0:return n.next=2,Object(l["a"])({serviceInstanceId:e.id});case 2:a=n.sent,200==a.code&&(t.$message.success("删除成功"),t.search());case 4:case"end":return n.stop()}}),n)}))))},handleRestart:function(e){var t=this;this.$msgbox({message:"确定重启此服务？",confirmButtonText:"重启",cancelButtonText:"取消",showCancelButton:!0,showConfirmButton:!0}).then(Object(i["a"])(Object(o["a"])().mark((function n(){var a;return Object(o["a"])().wrap((function(n){while(1)switch(n.prev=n.next){case 0:return n.next=2,Object(l["g"])({serviceId:e.id});case 2:a=n.sent,200==a.code&&(t.$message.success("重启成功"),t.search());case 4:case"end":return n.stop()}}),n)}))))},handleAdd:function(){this.dialog.addService.visible=!0;var e=this.dialog.addService.data;e.progressInterval&&clearInterval(e.progressInterval),this.dialog.addService.data={progress:null}},handleInstall:function(e){var t=this;return Object(i["a"])(Object(o["a"])().mark((function n(){return Object(o["a"])().wrap((function(n){while(1)switch(n.prev=n.next){case 0:return n.abrupt("return",new Promise(function(){var n=Object(i["a"])(Object(o["a"])().mark((function n(a){var r,s;return Object(o["a"])().wrap((function(n){while(1)switch(n.prev=n.next){case 0:if(r=t.dialog.addService.data,void 0!=r.commandId){n.next=13;break}return n.next=4,Object(l["f"])({serviceId:e.serviceId});case 4:return s=n.sent,r.commandId=s.data,n.next=8,Object(l["e"])({commandId:r.commandId});case 8:s=n.sent,r.progress=s.data.commandProgress,r.progressInterval=setInterval(Object(i["a"])(Object(o["a"])().mark((function e(){var t;return Object(o["a"])().wrap((function(e){while(1)switch(e.prev=e.next){case 0:return e.next=2,Object(l["e"])({commandId:r.commandId});case 2:t=e.sent,r.progress=t.data.commandProgress,100==r.progress&&(r.progressInterval&&clearInterval(r.progressInterval),a({code:200}));case 5:case"end":return e.stop()}}),e)}))),2e3),n.next=15;break;case 13:r.progressInterval&&clearInterval(r.progressInterval),a({code:200});case 15:case"end":return n.stop()}}),n)})));return function(e){return n.apply(this,arguments)}}()));case 1:case"end":return n.stop()}}),n)})))()},handleCancel:function(){var e=this.dialog.addService.data;e.progressInterval&&clearInterval(e.progressInterval)},handleSetInstall:function(e,t){console.log(e,t,"handleSetInstall"),e?this.dialog.addService.data.installService.push(t.id):this.dialog.addService.data.installService=this.dialog.addService.data.installService.filter((function(e){return e!==t.id})),console.log(this.dialog.addService.data.installService,"handleSetInstall")},handleSelect:function(e){var t=this;return Object(i["a"])(Object(o["a"])().mark((function n(){return Object(o["a"])().wrap((function(n){while(1)switch(n.prev=n.next){case 0:t.queryListForComponentHost=Object(s["a"])(Object(s["a"])({},t.queryListForComponentHost),{},{serviceName:e.componentName,serviceId:e.id}),t.$refs.tableComponentHostRef.getDataList(t.queryListForComponentHost);case 2:case"end":return n.stop()}}),n)})))()},getComponentList:function(e){var t=this;return Object(i["a"])(Object(o["a"])().mark((function e(){var n;return Object(o["a"])().wrap((function(e){while(1)switch(e.prev=e.next){case 0:return n=Object(s["a"])(Object(s["a"])({},t.queryListForComponent),{},{type:t.type}),delete n.pageNum,delete n.pageSize,e.next=5,Object(l["c"])(n);case 5:return e.abrupt("return",e.sent);case 6:case"end":return e.stop()}}),e)})))()},getComponentHostList:function(e){var t=this;return Object(i["a"])(Object(o["a"])().mark((function n(){var a;return Object(o["a"])().wrap((function(n){while(1)switch(n.prev=n.next){case 0:return a=Object(s["a"])(Object(s["a"])({},t.queryListForComponentHost),{},{limit:e.pageSize,offset:e.pageNum}),delete a.pageNum,delete a.pageSize,n.next=5,Object(l["b"])(a);case 5:return n.abrupt("return",n.sent);case 6:case"end":return n.stop()}}),n)})))()},search:function(){this.$refs.tableRef.getDataList(this.queryList)}}},p=d,m=(n("4089"),n("2877")),v=Object(m["a"])(p,a,r,!1,null,"2b58e918",null);t["a"]=v.exports}}]);