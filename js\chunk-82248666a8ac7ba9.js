(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-82248666"],{"1d62":function(t,e,a){},"5e19":function(t,e,a){},"5e61":function(t,e,a){"use strict";var i=a("5e19"),o=a.n(i);o.a},a6d3:function(t,e,a){"use strict";a.r(e);var i=function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("section",{staticClass:"gather"},[a("section",{staticClass:"gather-name"},[a("p",{staticClass:"title"},[t._v("采集模块:")]),t._v(" "),a("p",{staticClass:"name"},[t._v(t._s(t.moduleList[0]))]),t._v(" "),a("uxd-popover",{attrs:{placement:"bottom",title:"采集模块列表",trigger:"manual"},model:{value:t.showModuleList,callback:function(e){t.showModuleList=e},expression:"showModuleList"}},[a("div",{staticStyle:{height:"300px"}},[a("uxd-scrollbar",{staticClass:"hidden-horizontal",staticStyle:{height:"100%"}},t._l(t.moduleList,(function(e){return a("p",{key:e,staticStyle:{padding:"5px 0px"}},[a("uxd-tag",[t._v("\n                            "+t._s(e)+"\n                        ")])],1)})),0)],1),t._v(" "),a("uxd-button",{directives:[{name:"show",rawName:"v-show",value:t.moduleList&&t.moduleList.length>1,expression:"moduleList && moduleList.length > 1"}],attrs:{slot:"reference","is-icon":"",icon:t.showModuleList?"uxd-icon-double-arrow-left":"uxd-icon-double-arrow-right"},on:{click:t.handleShowModuleList},slot:"reference"})],1)],1),t._v(" "),a("div",{staticClass:"gather-chart"},[a("line-chart",{attrs:{data:t.flowStatus,tooltipFormatter:function(e){return t.flowTooltipFormatter(e,"M/s")},title:"采集速率"}})],1),t._v(" "),a("section",{staticClass:"detail-box"},[a("div",{staticClass:"detail-box-item"},[a("p",{staticClass:"detail-box-item-title"},[t._v("采集数据量")]),t._v(" "),a("div",{staticClass:"form"},[a("div",{staticClass:"form-item"},[a("p",{staticClass:"form-item-title"},[t._v("\n                        "+t._s(t.flowDetails.session.label)+"\n                    ")]),t._v(" "),a("p",{staticClass:"form-item-value"},[t._v("\n                        "+t._s(t.flowDetails.session.value)+"\n                    ")])]),t._v(" "),a("div",{staticClass:"form-item"},[a("p",{staticClass:"form-item-title"},[t._v("\n                        "+t._s(t.flowDetails.ether.label)+"\n                    ")]),t._v(" "),a("p",{staticClass:"form-item-value"},[t._v("\n                        "+t._s(t.flowDetails.ether.value)+"\n                    ")])]),t._v(" "),a("div",{staticClass:"form-item"},[a("p",{staticClass:"form-item-title"},[t._v("\n                        "+t._s(t.flowDetails.ip.label)+"\n                    ")]),t._v(" "),a("p",{staticClass:"form-item-value"},[t._v("\n                        "+t._s(t.flowDetails.ip.value)+"\n                    ")])])])]),t._v(" "),a("div",{staticClass:"detail-box-item"},[a("p",{staticClass:"detail-box-item-title"},[t._v("运行状态")]),t._v(" "),a("div",{staticClass:"form"},[a("div",{staticClass:"form-item"},[a("p",{staticClass:"form-item-title"},[t._v("探针状态")]),t._v(" "),a("p",{staticClass:"form-item-value"},[a("uxd-tag",{attrs:{effect:"dark",type:t.probeState.running?"success":"danger"}},[t._v("\n                            "+t._s(t.probeState.running?"正在运行":"停止运行")+"\n                        ")])],1)]),t._v(" "),a("div",{staticClass:"form-item"},[a("p",{staticClass:"form-item-title"},[t._v("运行内存限制")]),t._v(" "),a("p",{staticClass:"form-item-value"},[t._v("\n                        "+t._s(t.converToFlow(t.probeState.memoryLimit))+"\n                    ")])]),t._v(" "),a("div",{staticClass:"form-item"},[a("p",{staticClass:"form-item-title"},[t._v("内存使用")]),t._v(" "),a("p",{staticClass:"form-item-value"},[t._v("\n                        "+t._s(t.converToFlow(t.probeState.memoryUse))+"\n                    ")])])])]),t._v(" "),a("div",{staticClass:"detail-box-item"},[a("p",{staticClass:"detail-box-item-title"},[t._v("采集异常")]),t._v(" "),a("div",{staticClass:"form"},[a("div",{staticClass:"form-item"},[a("p",{staticClass:"form-item-title"},[t._v("\n                        "+t._s(t.flowDetails.drop.label)+"\n                    ")]),t._v(" "),a("p",{staticClass:"form-item-value"},[t._v("\n                        "+t._s(t.flowDetails.drop.value)+"\n                    ")])])])])])])},o=[],s=a("3835"),r=a("5530"),l=(a("b0c0"),a("d81d"),a("14d9"),a("b680"),a("d3b7"),a("d232")),n=a("4f8d"),c=a("2062");function u(t){return l["a"].get({url:n["f"].performance,params:t},{baseUrl:c["d"]})}function d(t){return l["a"].get({url:n["f"].probeStatistics,params:t},{baseUrl:c["d"]})}function f(t){return l["a"].get({url:n["f"].probeStatus,params:t},{baseUrl:c["d"]})}function p(t){return l["a"].get({url:n["f"].trafficCollecton,params:t})}var h=a("ef44"),m=function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("div",{staticClass:"echart-line",style:{height:t.height,width:t.width}},[0!==t.data.length?a("div",{staticStyle:{width:"100%",height:"100%"},attrs:{id:t.id}}):a("uxd-empty")],1)},v=[],_=(a("ac1f"),a("5319"),a("313e")),b=a.n(_);function w(t,e){var a;return function(){for(var i=this,o=arguments.length,s=new Array(o),r=0;r<o;r++)s[r]=arguments[r];a&&clearTimeout(a),a=setTimeout((function(){t.apply(i,s)}),e)}}var y={data:function(){return{chart:null}},watch:{data:{handler:function(t){var e=this;t&&this.$nextTick((function(){e.draw(t)}))},immediate:!0}},mounted:function(){var t=this;this.__resizeHanlder=w((function(){t.chart&&t.chart.resize()}),100),window.addEventListener("resize",this.__resizeHanlder)},destroyed:function(){window.removeEventListener("resize",this.__resizeHanlder),this.chart&&(b.a.dispose(this.chart),this.chart=null)}},x=a("5e9f"),g={mixins:[y],props:{data:{type:Array,default:function(){return[]}},title:{type:String,default:""},color:{type:Array,default:function(){return["#1d9ff9","#FF7571","#ff4242","#bb9c3a","#85cf1c","#5c86ff","#10adff","#ff4f5e","#ff9757","#5ddbab"]}},width:{type:String,default:"100%"},height:{type:String,default:"100%"},smooth:{type:Boolean,default:!0},showAreaStyle:{type:Boolean,default:!0},tooltipFormatter:{type:Function,default:void 0}},created:function(){this.id="chart-line"+(Math.random()+"").replace(".","_")},methods:{draw:function(){var t=this,e=document.getElementById(this.id);if(e){if(this.chart=b.a.init(e),0===this.data.length&&this.chart)return this.chart.clear(),void this.chart.dispose();var a=[],i=this;this.data.map((function(t,e){var o={type:"line",data:t.data,showSymbol:!0,smooth:i.smooth,hoverAnimation:!1,name:t.name,lineStyle:{color:i.color[e],width:2},itemStyle:{normal:{color:i.color[e],borderColor:"#FFFFFF",borderWidth:2,label:{show:!0,position:"top"}}},areaStyle:{color:{type:"linear",x:0,y:0,x2:0,y2:1,colorStops:[{offset:0,color:Object(x["a"])(i.color[e],!0,.4)},{offset:1,color:Object(x["a"])(i.color[e],!0,.01)}],global:!1}},symbol:"circle",symbolSize:[9,9]},s=o.type,r=o.data,l=o.showSymbol,n=o.smooth,c=o.hoverAnimation,u=o.name,d=o.lineStyle,f={type:s,data:r,showSymbol:l,smooth:n,hoverAnimation:c,name:u,lineStyle:d,itemStyle:{normal:{color:"#c2d4e9",borderColor:i.color[e],borderWidth:1,label:{show:!0,position:"top"}}}};a.push(i.showAreaStyle?o:f)}));var o={title:{show:!0,text:this.title||"",textStyle:{color:"rgb(194, 212, 233)"}},tooltip:{trigger:"axis",axisPointer:{animation:!0}},grid:{left:"0%",right:"0%",top:"15%",bottom:"0%",containLabel:!0},xAxis:[{type:"category",data:this.data[0]["xAxisData"],axisLine:{show:!0,lineStyle:{color:"transparent"}},axisTick:{show:!1},axisLabel:{textStyle:{color:"#c2d4e9",fontSize:12}},boundaryGap:["20%","20%"]}],yAxis:[{axisTick:{show:!1},axisLine:{show:!1,lineStyle:{color:"#c2d4e9"}},axisLabel:{textStyle:{color:"#c2d4e9",fontSize:12}},splitLine:{show:!0,lineStyle:{color:"#263fa1",opacity:.75}}}],series:a};this.tooltipFormatter&&(o.tooltip.formatter=this.tooltipFormatter),this.chart.setOption(o),this.__resizeHanlder(),this.chart.off("click"),this.chart.on("click",(function(e){t.$emit("nodeClick",e.name)}))}}}},S=g,C=(a("5e61"),a("2877")),L=Object(C["a"])(S,m,v,!1,null,null,null),D=L.exports,F={components:{LineChart:D},data:function(){return{flowStatus:[],converToFlow:h["a"],flowDetails:{drop:{label:"采集丢包率",value:""},session:{label:"会话数量",value:""},flow:{label:"双向流比例",value:""},pps:{label:"数据包处理PPS",value:""},ether:{label:"以太网数据成分",value:"",span:2},ip:{label:"IP数据包成分",value:"",span:2}},proPortData:{tcp_port_percent:[],udp_port_percent:[],transfer_port_percent:[]},probeState:{running:!1,memoryLimit:0,memoryUse:0},moduleList:[],refreshInterval:null,showModuleList:!1}},mounted:function(){var t=this;this.getDataList(),this.$route.meta.refreshInterval&&0!==this.$route.meta.refreshInterval&&(this.refreshInterval=setInterval((function(){t.getDataList()}),1e3*this.$route.meta.refreshInterval))},destroyed:function(){this.refreshInterval&&clearInterval(this.refreshInterval)},methods:{flowTooltipFormatter:function(t,e){var a="",i=t[0].value+e;return a=t[0].name+"<br/>"+t[0].marker+"："+i,a},getDataList:function(){this.getTrafficCollecton(),this.getProbePerformance(),this.getProbeStatistics(),this.getProbeStatus()},getProbePerformance:function(){var t=this;u().then((function(e){var a=e.data,i=a.speed,o=a.ether_protocol,s=a.ip_protocol,r=a.packet_pps,l=a.drop_rate,n=a.double_flow,c=a.flow_pps,u={data:[],xAxisData:[]};i.map((function(t){var e=t.time,a=t.speed;u.xAxisData.push(e),u.data.push({value:a.toFixed(3)})})),t.flowStatus=[u],t.flowDetails.drop.value=l,t.flowDetails.session.value=c,t.flowDetails.flow.value=n,t.flowDetails.pps.value=r,t.flowDetails.ether.value=o,t.flowDetails.ip.value=s}))},getProbeStatistics:function(){var t=this;d().then((function(e){var a=e.data,i=a.tcp_port_percent,o=a.transfer_port_percent,s=a.udp_port_percent,l={data:[],xAxisData:[]};o&&o.map((function(t){var e=t.port_name,a=t.percent;l.xAxisData.push(e),l.data.push({value:a.toFixed(3)})})),t.proPortData.transfer_port_percent=[l],t.proPortData.tcp_port_percent=i.map((function(t){return Object(r["a"])(Object(r["a"])({},t),{},{percent:"".concat(t.percent.toFixed(3),"%")})})),t.proPortData.udp_port_percent=s.map((function(t){return Object(r["a"])(Object(r["a"])({},t),{},{percent:"".concat(t.percent.toFixed(3),"%")})}))}))},getProbeStatus:function(){var t=this;f().then((function(e){var a=Object(s["a"])(e.data,1),i=a[0];if(i){var o=i.mem_limit,r=i.mem_use,l=i.running;t.probeState.running=l,t.probeState.memoryLimit=1024*o,t.probeState.memoryUse=1024*r}}))},getTrafficCollecton:function(){var t=this;this.moduleList=[],p().then((function(e){t.moduleList=e.data}))},handleShowModuleList:function(){this.showModuleList=!this.showModuleList}}},P=F,k=(a("aecf"),Object(C["a"])(P,i,o,!1,null,"042ac9da",null));e["default"]=k.exports},aecf:function(t,e,a){"use strict";var i=a("1d62"),o=a.n(i);o.a}}]);