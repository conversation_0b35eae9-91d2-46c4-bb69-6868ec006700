(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-9bd96ca0"],{"0c29":function(t,e,a){"use strict";var r=function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("section",{class:t.prefixCls},[a("p",{class:[t.prefixCls+"-title",1==t.border?"has-border":null]},[a("span",{class:t.prefixCls+"-title__text"},[t._v(t._s(t.title))]),t._v(" "),t._t("right")],2),t._v(" "),a("section",[t._t("default")],2)])},n=[],i=a("ef44"),s={name:"CommonSection",props:{title:{type:String,default:""},border:{type:Boolean,default:!1}},computed:{prefixCls:function(){var t=Object(i["e"])("section"),e=t.prefixCls;return e}}},d=s,o=(a("52b1"),a("2877")),c=Object(o["a"])(d,r,n,!1,null,"51fc79ae",null);e["a"]=c.exports},2628:function(t,e,a){},"3ad2":function(t,e,a){},"440cc":function(t,e,a){},"52b1":function(t,e,a){"use strict";var r=a("440cc"),n=a.n(r);n.a},"71f3":function(t,e,a){"use strict";a.r(e);var r=function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("div",{class:t.prefixClass},[a("div",{class:t.prefixClass+"--dashbord-list"},[a("common-section",{attrs:{title:"仪表盘列表",border:!0}},[a("template",{slot:"right"},[a("span",{staticClass:"confirm-btn",on:{click:t.handleAdd}},[t._v("创建")]),t._v(" "),a("span",{staticClass:"cancel-btn",on:{click:t.search}},[t._v("刷新")])]),t._v(" "),a("edr-table",{ref:"tableRef",attrs:{tableFieldList:t.tableFieldList,api:t.dashboardListApi,index:0,header:!1},on:{"row-click":t.handleSelect}},[a("uxd-table-column",{attrs:{width:"80px",label:"操作"},scopedSlots:t._u([{key:"default",fn:function(e){var r=e.row;return[a("uxd-button",{attrs:{type:"text",primary:""},on:{click:function(e){return t.handleDelete(r)}}},[t._v("\n                            删除\n                        ")])]}}])})],1)],2),t._v(" "),a("common-add-edit-dialog",{attrs:{visible:t.dialog.addEditDialog.visible,title:t.dialog.addEditDialog.title,data:t.dialog.addEditDialog.data,config:t.dialog.addEditDialog.config},on:{"update:visible":function(e){return t.$set(t.dialog.addEditDialog,"visible",e)},success:t.search}})],1),t._v(" "),a("div",{class:t.prefixClass+"--chart-list"},[a("dashboard-charts",{attrs:{dashboard:t.dashboard}})],1)])},n=[],i=a("c7eb"),s=a("1da1"),d=(a("ac1f"),a("841c"),a("fe5b")),o=a("2ef0"),c=a.n(o),l=function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("div",{class:""+t.prefixClass},[a("common-section",{attrs:{title:"仪表盘详情",border:!0}},[a("template",{slot:"right"},[t.dashboard?a("span",{staticClass:"confirm-btn",on:{click:t.handleAdd}},[t._v("\n                构建图表\n            ")]):t._e(),t._v(" "),a("span",{staticClass:"cancel-btn",on:{click:t.getCharts}},[t._v("刷新")]),t._v(" "),a("common-add-edit-dialog",{attrs:{visible:t.dialog.addEditDialog.visible,title:t.dialog.addEditDialog.title,data:t.dialog.addEditDialog.data,config:t.dialog.addEditDialog.config},on:{"update:visible":function(e){return t.$set(t.dialog.addEditDialog,"visible",e)},success:t.getCharts}})],1),t._v(" "),a("div",{class:t.prefixClass+"--list"},[t.dashboardChartList&&t.dashboardChartList.length>0?a("div",{class:t.prefixClass+"--list--content"},[a("uxd-scrollbar",[a("div",{staticClass:"dashboard-chart-list"},t._l(t.dashboardChartList,(function(e){return a("div",{key:e.id,staticClass:"dashboard-chart-item"},[e.data?a("common-section",{staticClass:"dashboard-section",attrs:{title:e.name,border:!0}},[a("template",{slot:"right"},[a("uxd-button",{attrs:{"is-icon":"",icon:"uxd-icon-delete"},on:{click:function(a){return t.handleDelete(e)}}}),t._v(" "),a("uxd-button",{staticStyle:{"margin-left":"0px"},attrs:{"is-icon":"",icon:"uxd-icon-more-vert"},on:{click:function(a){return t.handleEdit(e)}}})],1),t._v(" "),"柱状图"===t.DASHBOARD_CHART_TYPE_MAP.get(e.chartType)?a("echarts-bar",{attrs:{xAxis:e.data.xAxis,series:e.data.series,chartId:"echartRefs-"+e.id}}):"折线图"===t.DASHBOARD_CHART_TYPE_MAP.get(e.chartType)?a("echarts-line",{attrs:{xAxis:e.data.xAxis,series:e.data.series,chartId:"echartRefs-"+e.id}}):a("echarts-pie",{attrs:{series:e.data.series,chartId:"echartRefs-"+e.id}})],2):t._e()],1)})),0)])],1):a("uxd-empty")],1)],2)],1)},u=[],b=a("b85c"),h=(a("d81d"),a("a630"),a("3ca3"),a("d3b7"),a("ddb0"),a("159b"),a("0c29")),f=(a("4ec9"),new Map([[1,"聚合"],[2,"按时间累加"]])),p=new Map([["m","分"],["h","时"],["d","天"],["w","周"],["M","月"]]),g=new Map([[1,"柱状图"],[2,"折线图"],[3,"饼图"]]),m=a("5fda"),v=(a("313e"),a("f656")),x={components:{CommonSection:h["a"],echartsPie:m["c"],echartsLine:m["b"],echartsBar:m["a"]},props:{dashboard:{type:Object}},watch:{dashboard:{handler:function(t){null==t?this.dashboardChartList=[]:this.getCharts()},immediate:!0}},data:function(){return{prefixClass:"dashboard-charts-page",DASHBOARD_CHART_TYPE_MAP:g,dashboardChartList:[],dialog:{addEditDialog:{visible:!1,title:"创建仪表盘",data:{},config:{props:[{prop:"name",label:"图表名称",rules:[{required:!0,message:"请输入图表名称",trigger:"blur"},Object(v["b"])()]},{prop:"dataSource",label:"数据源",mapRemoteApi:d["g"],mapRemoteQuery:function(t,e){return{name:t}},mapRemoteResultHandle:function(t){return t.map((function(t){return{label:t,value:t}}))},map:[],rules:[{required:!0,message:"请选择数据源",trigger:"blur"}]},{prop:"operator",label:"计算方式",map:Array.from(f.keys()).map((function(t){return{label:f.get(t),value:t}})),rules:[{required:!0,message:"请选择计算方式",trigger:"blur"}],handleChange:this.handleChange},{prop:"fields",label:"字段",type:"array",limit:1,mapRemoteApi:d["e"],mapRemoteQuery:function(t,e){return{index:e["dataSource"],keywords:t,type:"聚合"==f.get(e["operator"])?"string":"number"}},mapRemoteResultHandle:function(t){return t.map((function(t){return{label:t,value:t}}))},map:[],rules:[{required:!0,message:"请选择字段",trigger:"blur"}]},{prop:"timeField",label:"时间字段",mapRemoteApi:d["e"],mapRemoteQuery:function(t,e){return{index:e["dataSource"],keywords:t,type:"date"}},mapRemoteResultHandle:function(t){return t.map((function(t){return{label:t,value:t}}))},map:[],showHandle:function(t){return"按时间累加"==f.get(t["operator"])},rules:[{required:!0,message:"请选择时间字段",trigger:"blur"}]},{prop:"timeUnit",label:"时间单位",map:Array.from(p.keys()).map((function(t){return{label:p.get(t),value:t}})),showHandle:function(t){return"按时间累加"==f.get(t["operator"])},rules:[{required:!0,message:"请选择时间单位",trigger:"blur"}]},{prop:"timeInterval",label:"时间间隔",showHandle:function(t){return"按时间累加"==f.get(t["operator"])},rules:[{required:!0,message:"请选择时间间隔",trigger:"blur"}]},{prop:"chartType",label:"图表类型",map:Array.from(g.keys()).map((function(t){return{label:g.get(t),value:t}})),rules:[{required:!0,message:"请选择图表类型",trigger:"blur"}]}],addApi:d["b"],editApi:d["k"]}}}}},methods:{getCharts:function(){var t=this;return Object(s["a"])(Object(i["a"])().mark((function e(){var a,r;return Object(i["a"])().wrap((function(e){while(1)switch(e.prev=e.next){case 0:if(!t.dashboard){e.next=12;break}return e.next=3,Object(d["d"])({dashboardId:t.dashboard.id});case 3:if(a=e.sent,!a||200!=a.code){e.next=10;break}return r=a.data,t.setChartDefault(r),e.next=9,t.setChartData(r);case 9:t.dashboardChartList=r;case 10:e.next=13;break;case 12:t.dashboardChartList=[];case 13:case"end":return e.stop()}}),e)})))()},setChartDefault:function(t){t.forEach((function(t){t.data={xAxis:{data:[]},series:[{data:[]}]}}))},setChartData:function(t){var e=this;return Object(s["a"])(Object(i["a"])().mark((function a(){var r,n,s,o,c,l,u;return Object(i["a"])().wrap((function(a){while(1)switch(a.prev=a.next){case 0:r=Object(b["a"])(t),a.prev=1,r.s();case 3:if((n=r.n()).done){a.next=16;break}return s=n.value,a.prev=5,a.next=8,Object(d["c"])({id:s.id});case 8:o=a.sent,o&&200==o.code&&(c=o.data,l=c.x,u=c.y,"饼图"==g.get(s.chartType)?s.data=e.setPieData(l,u):s.data=e.setNotPieData(l,u)),a.next=14;break;case 12:a.prev=12,a.t0=a["catch"](5);case 14:a.next=3;break;case 16:a.next=21;break;case 18:a.prev=18,a.t1=a["catch"](1),r.e(a.t1);case 21:return a.prev=21,r.f(),a.finish(21);case 24:case"end":return a.stop()}}),a,null,[[1,18,21,24],[5,12]])})))()},setPieData:function(t,e){return{series:[{data:t.map((function(t,a){return{name:t,value:e[a]}}))}]}},setNotPieData:function(t,e){return{xAxis:{data:t,axisLabel:{show:!1}},series:[{data:e}]}},handleAdd:function(){this.dialog.addEditDialog.title="创建图表",this.dialog.addEditDialog.visible=!0,this.dialog.addEditDialog.data={dashboardId:this.dashboard.id,name:"",dataSource:"",operator:1,fields:[],timeField:"",timeUnit:"m",timeInterval:1,chartType:1}},handleEdit:function(t){this.dialog.addEditDialog.title="编辑图表",this.dialog.addEditDialog.visible=!0;var e=c.a.cloneDeep(t);delete e.data,this.dialog.addEditDialog.data=e},handleDelete:function(t){var e=this;this.$msgbox({message:"确定删除此图表？",confirmButtonText:"删除",cancelButtonText:"取消",showCancelButton:!0,showConfirmButton:!0,confirmButtonClass:"uxd-button--danger"}).then(Object(s["a"])(Object(i["a"])().mark((function a(){var r;return Object(i["a"])().wrap((function(a){while(1)switch(a.prev=a.next){case 0:return a.next=2,Object(d["i"])({id:t.id});case 2:r=a.sent,r&&200==r.code&&e.getCharts();case 4:case"end":return a.stop()}}),a)}))))},handleChange:function(t,e,a){console.log(e,"handleChange"),"operator"==e.prop&&(a.fields=[])}}},C=x,D=(a("fcde"),a("2877")),w=Object(D["a"])(C,l,u,!1,null,"4c5e85a2",null),_=w.exports,y={components:{dashboardCharts:_,CommonSection:h["a"]},data:function(){var t,e,a;return{prefixClass:"dashboard-page",dashboardListApi:d["f"],dialog:{addEditDialog:{visible:!1,title:"创建仪表盘",data:{},config:{props:(null===(t=this.$store.state.system)||void 0===t?void 0:null===(e=t.config)||void 0===e?void 0:null===(a=e.table)||void 0===a?void 0:a.dashboard)||[],addApi:d["a"],editApi:d["j"]}}},dashboard:null}},computed:{tableFieldList:function(){var t,e,a;return(null===(t=this.$store.state.system)||void 0===t?void 0:null===(e=t.config)||void 0===e?void 0:null===(a=e.table)||void 0===a?void 0:a.dashboard)||[]}},methods:{search:function(){this.$refs.tableRef.getDataList()},handleSelect:function(t){var e=this;return Object(s["a"])(Object(i["a"])().mark((function a(){return Object(i["a"])().wrap((function(a){while(1)switch(a.prev=a.next){case 0:e.dashboard=null,e.$nextTick((function(){this.dashboard=t}));case 2:case"end":return a.stop()}}),a)})))()},handleAdd:function(){this.dialog.addEditDialog.title="创建仪表盘",this.dialog.addEditDialog.data={},this.dialog.addEditDialog.visible=!0},handleEdit:function(t){this.dialog.addEditDialog.title="编辑仪表盘",this.dialog.addEditDialog.data=c.a.cloneDeep(t),this.dialog.addEditDialog.visible=!0},handleDelete:function(t){var e=this;this.$msgbox({message:"确定删除此仪表盘？",confirmButtonText:"删除",cancelButtonText:"取消",showCancelButton:!0,showConfirmoButton:!0,confirmButtonClass:"uxd-button--danger"}).then(Object(s["a"])(Object(i["a"])().mark((function a(){var r;return Object(i["a"])().wrap((function(a){while(1)switch(a.prev=a.next){case 0:return a.next=2,Object(d["h"])({id:t.id});case 2:r=a.sent,r&&200==r.code&&(e.search(),e.handleSelect(null));case 4:case"end":return a.stop()}}),a)}))))}}},k=y,A=(a("fd42"),Object(D["a"])(k,r,n,!1,null,"6cb894c2",null));e["default"]=A.exports},f656:function(t,e,a){"use strict";a.d(e,"b",(function(){return r})),a.d(e,"a",(function(){return n})),a.d(e,"c",(function(){return i}));a("ac1f"),a("00b4"),a("d9e2");function r(){return{min:1,max:50,message:"长度在1-50个字符",trigger:["blur","change"]}}function n(){return{max:200,message:"长度最多为200个字符",trigger:["blur","change"]}}function i(){return{validator:function(t,e,a){var r=/^[a-zA-Z0-9!@#$%^&*()_=|\\（）+\/\u4e00-\u9fa5-]+$/;r.test(e)?a():a(new Error("只能包含中文、字母、数字和!@#$%^&*()_=|\\（）-+/"))},trigger:["blur","change"]}}},fcde:function(t,e,a){"use strict";var r=a("3ad2"),n=a.n(r);n.a},fd42:function(t,e,a){"use strict";var r=a("2628"),n=a.n(r);n.a},fe5b:function(t,e,a){"use strict";a.d(e,"f",(function(){return o})),a.d(e,"a",(function(){return c})),a.d(e,"j",(function(){return l})),a.d(e,"h",(function(){return u})),a.d(e,"d",(function(){return b})),a.d(e,"b",(function(){return h})),a.d(e,"k",(function(){return f})),a.d(e,"i",(function(){return p})),a.d(e,"c",(function(){return g})),a.d(e,"g",(function(){return m})),a.d(e,"e",(function(){return v}));var r=a("c7eb"),n=a("1da1"),i=(a("d3b7"),a("498a"),a("4de4"),a("d232")),s=a("4f8d"),d=a("2062");function o(t){return i["a"].post({url:s["c"].dashboardList,params:{pn:t.pageNum,ps:t.pageSize}},{baseUrl:d["b"]})}function c(t){return i["a"].post({url:s["c"].addDashboard,params:t},{baseUrl:d["b"]})}function l(t){return new Promise((function(t){t({status:200})}))}function u(t){return i["a"].post({url:s["c"].deleteDashboard,params:t},{baseUrl:d["b"]})}function b(t){return i["a"].post({url:s["c"].dashboardChartList,params:t},{baseUrl:d["b"]})}function h(t){return i["a"].post({url:s["c"].addDashboardChart,params:t},{baseUrl:d["b"]})}function f(t){return i["a"].post({url:s["c"].editDashboardChart,params:t},{baseUrl:d["b"]})}function p(t){return i["a"].post({url:s["c"].deleteDashboardChart,params:t},{baseUrl:d["b"]})}function g(t){return i["a"].post({url:s["c"].dashboardChartData,params:t},{baseUrl:d["b"]})}function m(t){return i["a"].post({url:s["c"].dashboardSourceList,params:t},{baseUrl:d["b"]})}function v(t){return x.apply(this,arguments)}function x(){return x=Object(n["a"])(Object(r["a"])().mark((function t(e){var a;return Object(r["a"])().wrap((function(t){while(1)switch(t.prev=t.next){case 0:return t.next=2,i["a"].post({url:s["c"].dashboardFieldList,params:{index:e.index}},{baseUrl:d["b"]});case 2:return a=t.sent,t.abrupt("return",{code:200,data:""==e.keywords.trim()?a.data[e.type]:a.data[e.type].filter((function(t){return-1!=t.indexOf(e.keywords)}))});case 4:case"end":return t.stop()}}),t)}))),x.apply(this,arguments)}}}]);