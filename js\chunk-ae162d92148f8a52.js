(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-ae162d92"],{4657:function(e,t,s){"use strict";var n=s("cf2e"),a=s.n(n);a.a},"5f01":function(e,t,s){"use strict";s.r(t);var n=function(){var e=this,t=e.$createElement,s=e._self._c||t;return s("div",{staticClass:"resource-page"},[s("edr-table",{ref:"tableRef",attrs:{tableFieldList:e.tableFieldList,api:e.getHostList,params:e.queryList},scopedSlots:e._u([{key:"hostname",fn:function(t){var n=t.row;return[s("span",{staticClass:"host-alarm",on:{click:function(t){return e.handleOpenAlarm(n.hostname)}}},[s("uxd-tooltip",{attrs:{"popper-class":"alarm-tooltip",effect:"dark",content:"查看主机【"+n.hostname+"】的告警信息",placement:"right-start"}},[s("uxd-icon-pro",{staticStyle:{cursor:"pointer"},attrs:{name:"uxd-icon-notifications-filled",theme:"danger"}})],1)],1),e._v(" "),e._v("\n            "+e._s(n.hostname)+"\n        ")]}},{key:"runningStatus",fn:function(t){var n=t.row;return[e.COMPONENT_SERVICE_RUNNINGSTATUS_MAP.get(n.runningStatus)?s("uxd-tag",{staticClass:"tag--status",attrs:{type:e.COMPONENT_SERVICE_RUNNINGSTATUS_MAP.get(n.runningStatus).theme}},[e._v("\n                "+e._s(e.COMPONENT_SERVICE_RUNNINGSTATUS_MAP.get(n.runningStatus).label)+"\n            ")]):e._e()]}},{key:"cpuUsage",fn:function(e){var t=e.row;return[s("uxd-progress",{attrs:{"stroke-width":8,percentage:parseFloat((t.cpuUsage||"0%").replace("%",""))}})]}},{key:"memoryUsage",fn:function(t){var n=t.row;return[s("div",{staticClass:"custom-progress"},[s("uxd-progress",{attrs:{"stroke-width":8,percentage:n.memPercent}}),e._v(" "),s("span",{staticClass:"progress-text"},[e._v("\n                    "+e._s(n.memoryUsage)+"/"+e._s(n.totalMemory)+"\n                ")])],1)]}},{key:"diskUsage",fn:function(t){var n=t.row;return[s("div",{staticClass:"custom-progress"},[s("uxd-progress",{attrs:{"stroke-width":8,percentage:n.diskPercent}}),e._v(" "),s("p",{staticClass:"progress-text"},[e._v("\n                    "+e._s(n.diskUsage)+"/"+e._s(n.totalDisk)+"\n                ")])],1)]}},{key:"networkRead",fn:function(t){var s=t.row;return[e._v("\n            "+e._s(Number(s.networkRead||"0").toFixed(2))+"\n        ")]}},{key:"networkWrite",fn:function(t){var s=t.row;return[e._v("\n            "+e._s(Number(s.networkWrite||"0").toFixed(2))+"\n        ")]}}])},[s("template",{slot:"search-header-form"},[s("uxd-form-item",{attrs:{label:"主机IP"}},[s("uxd-input",{attrs:{placeholder:"请输入主机IP"},model:{value:e.queryList.ip,callback:function(t){e.$set(e.queryList,"ip",t)},expression:"queryList.ip"}})],1),e._v(" "),s("uxd-form-item",{attrs:{label:"运行状态"}},[s("uxd-select",{attrs:{placeholder:"请选择"},model:{value:e.queryList.running_status,callback:function(t){e.$set(e.queryList,"running_status",t)},expression:"queryList.running_status"}},e._l(Array.from(e.COMPONENT_SERVICE_RUNNINGSTATUS_MAP.entries()),(function(e){var t=e[0],n=e[1];return s("uxd-option",{key:t,attrs:{label:n.label,value:t}})})),1)],1)],1),e._v(" "),s("template",{slot:"search-header-opt"},[s("span",{staticClass:"confirm-btn search-btn",on:{click:e.search}},[e._v("\n                搜索\n            ")]),e._v(" "),s("span",{staticClass:"cancel-btn reset-btn",on:{click:e.reset}},[e._v("\n                重置\n            ")])]),e._v(" "),e._v(" "),e._v(" "),e._v(" "),e._v(" "),e._v(" "),e._v(" "),e._v(" "),s("template",{slot:"search-header-form"})],2)],1)},a=[],r=s("c7eb"),i=s("5530"),o=s("1da1"),u=(s("4de4"),s("d3b7"),s("ac1f"),s("841c"),s("14d9"),s("d232")),c=s("4f8d"),l=(s("2ef0"),s("2062"));function p(e){return console.log(e,"getHostListApi"),u["a"].post({url:c["c"].getHostList,params:Object(i["a"])(Object(i["a"])({},e),{},{no_page:!0})},{baseUrl:l["b"]})}var d=s("6458"),f=s("daff"),m={components:{},computed:{tableFieldList:function(){var e,t,s;return(null===(e=this.$store.state.system)||void 0===e?void 0:null===(t=e.config)||void 0===t?void 0:null===(s=t.table)||void 0===s?void 0:s.dataResource.filter((function(e){return!1!==e.show})))||[]}},data:function(){return{COMPONENT_SERVICE_RUNNINGSTATUS_MAP:d["b"],queryList:{no_page:!1,ip:"",running_status:""}}},methods:{reset:function(){this.queryList={no_page:!1,ip:"",running_status:""},this.search()},search:function(){this.$refs.tableRef.getDataList(this.queryList)},getHostList:function(e){var t=this;return Object(o["a"])(Object(r["a"])().mark((function s(){var n;return Object(r["a"])().wrap((function(s){while(1)switch(s.prev=s.next){case 0:return n=Object(i["a"])(Object(i["a"])({},t.queryList),{},{limit:e.pageSize,offset:e.pageNum}),delete n.pageNum,delete n.pageSize,s.next=5,p(n);case 5:return s.abrupt("return",s.sent);case 6:case"end":return s.stop()}}),s)})))()},handleOpenAlarm:function(e){this.$router.push({path:f["v"],query:{hostname:e}})}}},_=m,h=(s("4657"),s("2877")),v=Object(h["a"])(_,n,a,!1,null,"7bf95eae",null);t["default"]=v.exports},6458:function(e,t,s){"use strict";s.d(t,"a",(function(){return n})),s.d(t,"b",(function(){return a}));s("4ec9"),s("d3b7"),s("3ca3"),s("ddb0"),new Map([[0,{label:"停止",theme:"info"}],[1,{label:"告警",theme:"danger"}],[2,{label:"正常",theme:"success"},,]]);var n=new Map([["UNKNOWN",{label:"未知",theme:"info"}],["CONFIGURING",{label:"已配置",theme:"success"}],["FAILED",{label:"失败",theme:"danger"}],["EXPIRED",{label:"过期",theme:"warning"}],["SYNCHRONIZED",{label:"已同步",theme:"success"}]]),a=new Map([["1",{label:"正常",theme:"success"}],["2",{label:"掉线",theme:"info"}],["3",{label:"异常",theme:"danger"}]])},cf2e:function(e,t,s){}}]);