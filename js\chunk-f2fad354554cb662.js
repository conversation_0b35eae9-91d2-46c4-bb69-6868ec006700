(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-f2fad354"],{a8c9:function(e,t,a){"use strict";var n=a("f10e"),i=a.n(n);i.a},b6d6:function(e,t,a){"use strict";a.r(t);var n=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("edr-table",{ref:"tableRef",attrs:{tableFieldList:e.tableFieldList,api:e.sceneListApi,params:e.queryList,header:!1}},[a("template",{slot:"search-header-form"}),e._v(" "),a("uxd-table-column",{attrs:{width:"100px",label:"操作"},scopedSlots:e._u([{key:"default",fn:function(t){var n=t.row;return[a("span",{staticClass:"operation",on:{click:function(t){return e.handleShowDetail(n)}}},[e._v("\n                查看详情\n            ")])]}}])}),e._v(" "),a("scene-detail-dialog",{attrs:{visible:e.dialog.sceneDetail.visible,data:e.dialog.sceneDetail.data},on:{"update:visible":function(t){return e.$set(e.dialog.sceneDetail,"visible",t)}}})],2)},i=[],s=(a("ac1f"),a("841c"),a("d3b7"),a("14d9"),a("d232")),l=a("4f8d"),r=a("2ef0"),c=a.n(r),d=!1;function o(e){if(d)return new Promise((function(e){e({code:200,data:[{sceneId:"1232829749871",sceneName:"场景1",taskType:"红蓝对抗1",createTime:"2025-06-16 00:00:00",level:"初级"},{sceneId:"1232829749872",sceneName:"场景2",taskType:"红蓝对抗2",createTime:"2025-06-16 00:10:00",level:"中级"},{sceneId:"1232829749872",sceneName:"场景2",taskType:"红蓝对抗3",createTime:"2025-06-16 00:20:00",level:"高级"}],total:2})}));var t=c.a.cloneDeep(e);return t.page=t.pageNum,t.limit=t.pageSize,delete t.pageNum,delete t.pageSize,s["a"].post({url:l["e"].sceneList,params:t})}function u(e){return d?new Promise((function(e){e({code:200,msg:"操作成功",data:[{name:"流量采集",datas:[{name:"IP会话",index:"ip*"},{name:"DNS记录",index:"dns*"}]},{name:"终端采集",datas:[{name:"进程文件信息",index:"trans-type-fileinfo"},{name:"进程信息",index:"trans-type-process-info"}]},{name:"流量采集1",datas:[{name:"IP会话",index:"ip1*"},{name:"DNS记录",index:"dns1*"}]},{name:"终端采集1",datas:[{name:"进程文件信息",index:"trans-type-fileinfo1"},{name:"进程信息",index:"trans-type-process-info1"}]},{name:"流量采集2",datas:[{name:"IP会话",index:"ip2*"},{name:"DNS记录",index:"dns2*"}]},{name:"终端采集2",datas:[{name:"进程文件信息",index:"trans-type-fileinfo2"},{name:"进程信息",index:"trans-type-process-info2"}]},{name:"流量采集3",datas:[{name:"IP会话",index:"ip3*"},{name:"DNS记录",index:"dns3*"}]},{name:"终端采集3",datas:[{name:"进程文件信息",index:"trans-type-fileinfo3"},{name:"进程信息",index:"trans-type-process-info3"}]},{name:"流量采集4",datas:[{name:"IP会话",index:"ip4*"},{name:"DNS记录",index:"dns4*"}]},{name:"终端采集4",datas:[{name:"进程文件信息",index:"trans-type-fileinfo4"},{name:"进程信息",index:"trans-type-process-info4"}]}]})})):s["a"].get({url:l["e"].sceneDetailType,params:e})}function p(e){return d?new Promise((function(t){var a=[{field:"sip",title:"源IP"},{field:"dip",title:"目的IP"},{field:"creatTime",title:"创建时间"},{field:"sip",title:"源IP"},{field:"dip",title:"目的IP"},{field:"creatTime",title:"创建时间"}];if("dns*"==e.index)for(var n=[{field:"sip",title:"源IP"},{field:"dip",title:"目的IP"},{field:"creatTime",title:"创建时间"}],i=0;i<5;i++)n.forEach((function(e){a.push(e)}));t({code:200,msg:"操作成功",data:a})})):s["a"].get({url:l["e"].sceneDetailDataMapping,params:e})}function f(e){var t=c.a.cloneDeep(e);return t.page=t.pageNum,t.limit=t.pageSize,delete t.pageNum,delete t.pageSize,delete t.keywords,console.log(t,"sceneDetailDataApi"),d?new Promise((function(e){for(var t=[],a=100,n=0;n<a;n++)t.push({sip:"***************",dip:"*******",creatTime:"2025-06-16 00:00:00"});setTimeout((function(){e({code:200,msg:"操作成功",data:t,total:a,realTotal:a})}),1e3)})):s["a"].post({url:l["e"].sceneDetailData,params:t})}var m=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("uxd-dialog",{attrs:{title:"场景详情",visible:e.visible,"custom-class":"scene-detail-dialog","before-close":e.handleClose,"append-to-body":""},on:{"update:visible":function(t){e.visible=t}}},[a("div",{ref:"sceneDetailRef",class:""+e.prefixCls},[a("div",{class:e.prefixCls+"--type-list"},[a("uxd-scrollbar",[a("uxd-menu",{ref:"menuRef",attrs:{"default-active":e.selectedSubType,"default-openeds":e.defaultOpeneds},on:{select:e.handleSelect}},e._l(e.sceneDetailType,(function(t){return a("uxd-submenu",{key:t.name,attrs:{index:t.name}},[a("template",{slot:"title"},[a("svg-icon",{staticStyle:{width:"14px",height:"14px","margin-right":"5px"},attrs:{name:"circle"}}),e._v(" "),a("span",{staticStyle:{"font-size":"16px"}},[e._v(e._s(t.name))])],1),e._v(" "),e._l(t.datas,(function(t){return a("uxd-menu-item",{key:t.index,attrs:{index:t.index}},[a("span",{staticStyle:{"font-size":"16px"}},[e._v(e._s(t.name))])])}))],2)})),1)],1)],1),e._v(" "),a("div",{class:e.prefixCls+"--data-list"},[a("edr-table",{ref:"tableRef",attrs:{tableFieldList:e.tableFieldList,api:e.sceneDetailDataApi,params:e.queryList,isMountedGetData:!1}},[a("template",{slot:"search-header-form"},[a("uxd-form-item",{attrs:{label:"关键字"}},[a("uxd-input",{attrs:{placeholder:"请输入关键字"},model:{value:e.queryList.keywords,callback:function(t){e.$set(e.queryList,"keywords",t)},expression:"queryList.keywords"}})],1)],1),e._v(" "),a("template",{slot:"search-header-opt"},[a("span",{staticClass:"confirm-btn search-btn",on:{click:e.search}},[e._v("\n            搜索\n          ")]),e._v(" "),a("span",{staticClass:"cancel-btn reset-btn",on:{click:e.reset}},[e._v("\n            重置\n          ")])])],2)],1)])])},h=[],b=a("c7eb"),y=a("1da1"),v=(a("e9c4"),a("d81d"),a("caad"),a("2532"),a("159b"),a("b0c0"),{props:{visible:{type:Boolean,default:!1},data:{type:Object,default:function(){return{}}}},data:function(){return{prefixCls:"scene-detail-page",sceneDetailDataApi:f,loading:!1,selectedSubType:null,sceneDetailType:[],defaultProps:{children:"datas",label:"name"},tableFieldList:[],queryList:{keywords:"",sceneId:"",params:JSON.stringify({conditions:[{connector:"and",field:"keywords",op:"equal",value:""}],indexName:"",indexType:"",sortField:"@createtime",sortOrder:"desc"})},defaultOpeneds:[]}},watch:{visible:{handler:function(e){e&&this.initData()}}},methods:{initData:function(){var e=this;return Object(y["a"])(Object(b["a"])().mark((function t(){return Object(b["a"])().wrap((function(t){while(1)switch(t.prev=t.next){case 0:return e.type="",e.sceneDetailType=[],e.selectedSubType=null,e.queryList.keywords="",t.next=6,e.getSceneDetailType();case 6:return t.next=8,e.getTableData();case 8:case"end":return t.stop()}}),t)})))()},getTableData:function(){var e=this;return Object(y["a"])(Object(b["a"])().mark((function t(){return Object(b["a"])().wrap((function(t){while(1)switch(t.prev=t.next){case 0:return t.next=2,e.getSceneDetailDataMapping();case 2:e.search();case 3:case"end":return t.stop()}}),t)})))()},getSceneDetailType:function(){var e=this;return Object(y["a"])(Object(b["a"])().mark((function t(){var a;return Object(b["a"])().wrap((function(t){while(1)switch(t.prev=t.next){case 0:return t.next=2,u({sceneId:e.data.sceneId});case 2:a=t.sent,200==a.code&&(e.sceneDetailType=a.data,e.sceneDetailType&&e.sceneDetailType.length>0&&e.sceneDetailType[0].datas.length>0&&(e.selectedSubType=e.sceneDetailType[0].datas[0].index),e.$nextTick((function(){e.expandAllSubMenus()})));case 4:case"end":return t.stop()}}),t)})))()},getSceneDetailDataMapping:function(){var e=this;return Object(y["a"])(Object(b["a"])().mark((function t(){var a;return Object(b["a"])().wrap((function(t){while(1)switch(t.prev=t.next){case 0:return t.next=2,p({index:e.selectedSubType});case 2:a=t.sent,200==a.code&&(e.tableFieldList=a.data.map((function(e){return{prop:e.field,label:e.title,minWidth:e.field.toLowerCase().includes("time")?200:160}})));case 4:case"end":return t.stop()}}),t)})))()},handleClose:function(){this.$emit("update:visible",!1)},handleSelect:function(e){this.selectedSubType=e,this.queryList.keywords="",this.getTableData()},search:function(){this.queryList.sceneId=this.data.sceneId,this.queryList.params=JSON.stringify({conditions:[{connector:"and",field:"keywords",op:"equal",value:this.queryList.keywords||""}],indexName:this.selectedSubType,indexType:this.selectedSubType,sortField:"@createtime",sortOrder:"desc"}),this.$refs.tableRef.getDataList(this.queryList)},reset:function(){this.queryList.keywords="",this.search()},expandAllSubMenus:function(){var e=[];this.sceneDetailType.forEach((function(t){e.push(t.name)})),this.defaultOpeneds=e}}}),x=v,D=(a("a8c9"),a("2877")),g=Object(D["a"])(x,m,h,!1,null,null,null),w=g.exports,T={components:{SceneDetailDialog:w},data:function(){return{sceneListApi:o,queryList:{},dialog:{sceneDetail:{visible:!1}}}},computed:{tableFieldList:function(){var e,t,a;return(null===(e=this.$store.state.system)||void 0===e?void 0:null===(t=e.config)||void 0===t?void 0:null===(a=t.table)||void 0===a?void 0:a.dataView)||[]}},methods:{handleShowDetail:function(e){this.dialog.sceneDetail.data=c.a.cloneDeep(e),this.dialog.sceneDetail.visible=!0},reset:function(){this.search()},search:function(){this.$refs.tableRef.getDataList(this.queryList)}}},S=T,k=Object(D["a"])(S,n,i,!1,null,null,null);t["default"]=k.exports},f10e:function(e,t,a){}}]);